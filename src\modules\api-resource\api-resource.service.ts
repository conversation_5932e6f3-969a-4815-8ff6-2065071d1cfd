import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  forwardRef,
} from '@nestjs/common';
import { CreateApiResourceDto } from './dto/create-api-resource.dto';
import { UpdateApiResourceDto } from './dto/update-api-resource.dto';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { S3Service } from 'src/providers/s3/s3.service';
import { ConfigService } from '@nestjs/config';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { basename } from 'path';
import { ApiResource, ApiResourceStatus, Group, PrismaClient, Prisma } from '@prisma/client';
import { GroupsService } from '../groups/groups.service';
import { CallbackInput, HostMap } from './dto/api-resource.interface';
import { LLMBackendService } from 'src/providers/llm-backend/llm-backend.service';
import { LLMModelsService } from '../llm-models/llm-models.service';
import { PromotableService } from '../change-management/interfaces/promotable-service.interface';
import OpenAPISchemaValidator, { OpenAPISchemaValidatorResult } from 'openapi-schema-validator';
import { load, YAMLException } from 'js-yaml';
import { OpenAPIV3 } from 'openapi-types';
import { MutilpleLevelFeatureFlagsModelDto } from '../mutilple-level-feature-flags/mutilple-level-feature-flags-model.dto';
import { isURL } from 'class-validator';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { ChatLlmModelDto } from '../llm-models/dto/chat-llm-model.dto';

@Injectable()
export class ApiResourceService implements PromotableService {
  private logger = new Logger(ApiResourceService.name);
  private DOC_PATH = 'api_resource/{GROUP_ID}/{CURRENT_TIME}_{FILE_NAME}';
  static IS_URL_OPTIONS = { allow_trailing_dot: true };
  static HOST_MAP_TEST_KEY = 'test';
  static HOST_MAP_PROD_KEY = 'prod';
  static HOST_MAP_FEATURE_FLAGS = 'CONNECT_API.ALLOWED_HOST_URL_MAP';
  private USER_FILTER_QUERY = {
    user: {
      select: {
        id: true,
        name: true,
        emails: true,
      },
    },
  };
  private OPEN_APIS_VALIDATOR = new OpenAPISchemaValidator({ version: 3 });
  private readonly CUSTOM_PARAM_GLOBAL_ALLOW_CONFIG = ['headers', 'pathParamReplace'];
  private readonly CUSTOM_PARAM_API_ALLOW_CONFIG = [
    'headers',
    'appendBody',
    'appendQueryParam',
    'pathParamReplace',
  ];

  constructor(
    private readonly prisma: PrismaService,
    private readonly s3Service: S3Service,
    private readonly configService: ConfigService,
    private readonly groupService: GroupsService,
    private readonly lLMBackendService: LLMBackendService,
    @Inject(forwardRef(() => LLMModelsService))
    private readonly lLMModelsService: LLMModelsService,
    private readonly featureFlagService: FeatureFlagService,
  ) {}

  async create(createApiResourceDto: CreateApiResourceDto, groupId: number): Promise<ApiResource> {
    const apiResource = await this.prisma.apiResource.findUnique({ where: { groupId } });
    await this.getHostMap(groupId, createApiResourceDto.hostUrl);
    if (apiResource) {
      throw new BadRequestException('The api resource already existed.');
    }
    await this.groupService.getGroup(groupId, {});
    this.validateCustomParam(createApiResourceDto.customParam);
    const apiResourceData = { ...createApiResourceDto, groupId } as ApiResource;
    return this.prisma.apiResource.create({ data: apiResourceData });
  }

  findByGroupId(groupId: number): Promise<ApiResource> {
    return this.prisma.apiResource.findUnique({
      include: this.USER_FILTER_QUERY,
      where: { groupId },
    });
  }

  async findOne(id: number): Promise<ApiResource> {
    return this.prisma.apiResource.findUnique({ where: { id } });
  }

  async update(
    id: number,
    groupId: number,
    updateApiResourceDto: UpdateApiResourceDto,
  ): Promise<ApiResource> {
    await this.getHostMap(groupId, updateApiResourceDto.hostUrl);
    await this.verifyGroupAndResource(groupId, id);
    this.validateCustomParam(updateApiResourceDto.customParam);
    return this.prisma.apiResource.update({
      include: this.USER_FILTER_QUERY,
      where: { id },
      data: { ...updateApiResourceDto } as ApiResource,
    });
  }

  validateCustomParam(customParam: string) {
    if (!customParam) {
      return;
    }
    const json = JSON.parse(customParam);
    Object.keys(json).forEach((customParamKey: string) => {
      const customParamConfig = json[customParamKey];
      const customParamConfigKey = Object.keys(customParamConfig);
      const customParamCheck = customParamConfigKey.filter((item) =>
        customParamKey === '@global'
          ? !this.CUSTOM_PARAM_GLOBAL_ALLOW_CONFIG.includes(item)
          : !this.CUSTOM_PARAM_API_ALLOW_CONFIG.includes(item),
      );
      if (customParamCheck.length >= 1) {
        throw new ApiException(
          ErrorCode.CONNECT_API_CUSTOM_PARAM_INVALID.replace(
            '{CustomParam}',
            customParamCheck.join(','),
          ),
        );
      }
    });
  }

  async remove(id: number, groupId: number): Promise<ApiResource> {
    const { apiResource } = await this.verifyGroupAndResource(groupId, id);
    const deleteApiResource = this.prisma.$transaction(async (transactions) => {
      const deleteApiResource = await transactions.apiResource
        .delete({ where: { id: id } })
        .catch((error) => {
          this.logger.error(error);
          throw new InternalServerErrorException('delete api resource failed');
        });
      if (apiResource.docPath?.length <= 0) {
        await this.deleteApiResourceDoc(apiResource.groupId, apiResource.id, false);
      }
      return deleteApiResource;
    });
    return deleteApiResource;
  }

  gptServiceGenerationCallback(id: number, callbackInput: CallbackInput): Promise<ApiResource> {
    if (callbackInput.status == ApiResourceStatus.FAILED) {
      return this.prisma.apiResource.update({ where: { id: id }, data: { ...callbackInput } });
    }
    return this.prisma.apiResource.update({
      where: { id: id },
      data: { ...callbackInput, generatedAt: new Date() },
    });
  }

  async uploadApiDocumentFile(
    file: Express.Multer.File,
    apiResource: ApiResource,
    groupId: number,
    userId: number,
  ): Promise<ApiResource> {
    const group = await this.groupService.getGroup(groupId, {});
    const llmModel = await this.lLMModelsService.findOneByGroupId(groupId);
    this.validateOpenAPISchema(file);
    let key;
    let fileKey;
    try {
      const env = group.env;
      if (!this.configService.get<string>(`s3.staticFilesBuckets.${env}`)) {
        throw new InternalServerErrorException('Static file bucket not set');
      }
      const currentTime = new Date().getTime();
      fileKey = this.DOC_PATH.replace('{GROUP_ID}', groupId.toString())
        .replace('{CURRENT_TIME}', currentTime.toString())
        .replace('{FILE_NAME}', basename(file.originalname));
      const { Key: uploadFileKey } = await this.s3Service.upload(
        fileKey,
        file.originalname,
        file.buffer,
        file.mimetype,
        group.id,
        this.configService.get<string>(`s3.staticFilesBuckets.${env}`),
        false,
      );
      key = uploadFileKey;
    } catch (err) {
      this.logger.error(err);
      throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
    if (!key) {
      throw new ApiException(ErrorCode.UPLOAD_FILE_TO_MODEL_FAILED);
    }
    apiResource.docPath = key;
    const res = await this.lLMBackendService
      .genApiResource(apiResource, llmModel.modelId, group.env)
      .catch((err) => {
        this.logger.error(err, 'Upload file failed');
        throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
      });
    const updateApiResponse = await this.prisma.apiResource.update({
      include: this.USER_FILTER_QUERY,
      where: {
        id: apiResource.id,
      },
      data: {
        docPath: fileKey,
        fileName: file.originalname,
        status: res.status,
        supportCalls: res?.supportCalls ?? {},
        fileBytesSize: file.size,
        uploadBy: userId,
        generatedAt: new Date(),
      },
    });
    return updateApiResponse;
  }

  private validateOpenAPISchema(file: Express.Multer.File): void {
    let openapiDoc: OpenAPIV3.Document;
    try {
      openapiDoc = load(file.buffer.toString(), { json: true }) as OpenAPIV3.Document;
    } catch (err) {
      if (err instanceof YAMLException) {
        throw new ApiException(ErrorCode.LOAD_YAML_FILE_FAILED, { error: err.message });
      }
      this.logger.error(err, 'load yaml file failed');
      throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
    if (!openapiDoc?.openapi || !openapiDoc.openapi?.startsWith('3.')) {
      throw new ApiException(ErrorCode.OPENAPI_VERSION_NOT_SUPPROT);
    }
    const valid: OpenAPISchemaValidatorResult = this.OPEN_APIS_VALIDATOR.validate(openapiDoc);
    Object.entries(openapiDoc.paths).filter((item) => {
      const method = Object.keys(item[1])[0];
      if (
        item?.[1]?.[method]?.requestBody &&
        (!item[1][method]?.requestBody?.content ||
          Object.values(item[1][method]?.requestBody?.content).length == 0)
      ) {
        throw new ApiException(
          ErrorCode.API_RESOURCE_UPLOAD_FILE_YAML_FORMAT_ERROR.replace(
            '{msg}',
            `The path \`${item[0]}\` request body format error`,
          ),
        );
      }
    });
    if (valid.errors && valid.errors?.length > 0) {
      const error = valid.errors.reduce((errorProperty, item) => {
        const [firstItem] = Object.values(item.params);
        errorProperty = {
          ...errorProperty,
          [firstItem]: {
            property: firstItem,
            message: item.message,
          },
        };
        return errorProperty;
      }, {});
      throw new ApiException(ErrorCode.OPENAPI_FORMAT_ERROR, error);
    }
  }

  /**
   * @description if the bot have connect api and (status = ApiResourceStatus.GENERATED &&  enable: true)
   * will append the connect api info in to the override.api_resource obj
   * @param groupId
   * @param chatLlmModelDto
   * @returns
   */
  async appendApiResourceAsOverridesDataByGroupId(
    groupId: number,
    chatLlmModelDto: ChatLlmModelDto,
  ) {
    const appendVariables = chatLlmModelDto?.appendVariables;
    const chatLlmModelRequest = { ...chatLlmModelDto };
    delete chatLlmModelRequest?.appendVariables;
    const apiResource = await this.prisma.apiResource.findFirst({
      where: {
        groupId: groupId,
        status: ApiResourceStatus.GENERATED,
        enable: true,
      },
    });
    if (!apiResource) {
      return chatLlmModelRequest;
    }
    let customParamString = apiResource.customParam;
    if (appendVariables) {
      const map = new Map(Object.entries(appendVariables));
      for (const key of map.keys()) {
        customParamString = customParamString.replace(`{${key}}`, map.get(key));
      }
    }
    const customParam = JSON.parse(customParamString);
    customParam.type = apiResource.apiResourceType;
    customParam.hostUrl = apiResource.hostUrl;
    chatLlmModelRequest.overrides = { ...chatLlmModelRequest.overrides, api_resource: customParam };
    return chatLlmModelRequest;
  }

  async fetchApiResourceDocFileStream(
    groupId: number,
    id: number,
  ): Promise<any> {
    const { group, apiResource } = await this.verifyGroupAndResource(groupId, id);
    return this.s3Service.getObjectUnsafe(
      this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`),
      apiResource.docPath,
    );
  }

  async deleteApiResourceDoc(
    groupId: number,
    apiResourceId: number,
    needUpdate: boolean = true,
  ): Promise<ApiResource> {
    const { group, apiResource } = await this.verifyGroupAndResource(groupId, apiResourceId);
    await this.s3Service
      .delete(
        this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`),
        apiResource.docPath,
      )
      .catch((error) => {
        this.logger.error(error);
        throw new ApiException(ErrorCode.DELETE_FILE_TO_MODEL_FAILED);
      });
    if (needUpdate) {
      return await this.prisma.apiResource.update({
        where: {
          id: apiResource.id,
        },
        data: {
          docPath: '',
          fileName: '',
          fileBytesSize: null,
          uploadBy: null,
          status: ApiResourceStatus.UNKNOWN,
        },
      });
    }
  }
  async verifyGroupAndResource(
    groupId: number,
    apiResourceId: number,
  ): Promise<{ group: Group; apiResource: ApiResource }> {
    const group = await this.groupService.getGroup(groupId, {});
    const apiResource = await this.findOne(apiResourceId);
    if (!apiResource) {
      throw new ApiException(ErrorCode.API_RESOURCE_NOT_FOUND);
    }
    if (group.id !== apiResource.groupId) {
      throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    }
    return {
      group,
      apiResource,
    };
  }

  async deleteEntityDataForSnapshot(
    groupId: number,
    entityId: string,
    entityData: any,
  ): Promise<void> {
    const group = await this.groupService.getGroup(entityData.groupId, {});
    await this.s3Service.delete(
      this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`),
      entityData.docPath,
    );
  }

  async generateEntityDataForSnapshot(groupId: number, entityId: string): Promise<object> {
    const group = await this.groupService.getGroup(groupId, {});
    const apiResource = await this.prisma.apiResource.findFirst({
      where: {
        groupId,
        id: parseInt(entityId),
      },
    });
    if (!apiResource) throw new ApiException(ErrorCode.API_RESOURCE_NOT_FOUND);
    const hostMap = await this.getHostMap(groupId, apiResource.hostUrl);
    if (apiResource.docPath?.length === 0 || apiResource.status !== ApiResourceStatus.GENERATED) {
      throw new ApiException(ErrorCode.API_RESOURCE_CREAT_SNAPSHOT_NOT_ALLOW);
    }
    const bucket = this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`);
    const currentTime = new Date().getTime().toString();
    const snapshotFileDir = this.DOC_PATH.replace('{GROUP_ID}', groupId.toString())
      .replace('{CURRENT_TIME}', currentTime)
      .replace('{FILE_NAME}', basename(apiResource.fileName));
    await this.s3Service
      .copyFileObject(bucket, apiResource.docPath, bucket, snapshotFileDir)
      .catch((error) => {
        this.logger.error(error, 'Failed copy s3 object ');
        throw new InternalServerErrorException('Failed copy s3 object');
      });
    apiResource.docPath = snapshotFileDir;
    apiResource.hostUrl = hostMap[ApiResourceService.HOST_MAP_PROD_KEY];
    return apiResource;
  }

  async promoteCreate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    entityData: any,
    operatorId: number,
  ): Promise<string> {
    const apiResource = await this.promoteApiResource(entityData, targetGroup);
    const createApiResource = await tx.apiResource.create({ data: apiResource });
    return createApiResource.id.toString();
  }

  async promoteUpdate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    entityData: any,
    operatorId: number,
  ): Promise<void> {
    const originApiResource = await this.findByGroupId(targetGroup.id);
    const apiResource = await this.promoteApiResource(
      entityData,
      targetGroup,
      originApiResource.docPath,
    );
    await tx.apiResource.update({
      where: { id: Number(targetEntityId) },
      data: { ...apiResource },
    });
  }

  async promoteDelete(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    operatorId: number,
  ): Promise<void> {
    const apiResource = await tx.apiResource.delete({ where: { id: Number(targetEntityId) } });
    await this.s3Service.delete(
      this.configService.get<string>(`s3.staticFilesBuckets.${targetGroup.env}`),
      apiResource.docPath,
    );
  }

  async checkPromotedEntityValid(targetEntityId: string) {
    return this.prisma.apiResource.findUnique({ where: { id: Number(targetEntityId) } });
  }

  private async promoteApiResource(
    entityData: ApiResource,
    targetGroup: Group,
    replaceDir?: string,
  ): Promise<ApiResource> {
    const llmModel = await this.lLMModelsService.findOneByGroupId(targetGroup.id);
    const sourceGroup = await this.groupService.getGroup(entityData.groupId, {});
    const sourceBucket = this.configService.get<string>(`s3.staticFilesBuckets.${sourceGroup.env}`);
    const bucket = this.configService.get<string>(`s3.staticFilesBuckets.${targetGroup.env}`);
    if (!replaceDir) {
      const currentTime = new Date().getTime().toString();
      replaceDir = this.DOC_PATH.replace('{GROUP_ID}', targetGroup.id.toString())
        .replace('{CURRENT_TIME}', currentTime)
        .replace('{FILE_NAME}', basename(entityData.fileName));
    }
    const fileKey = replaceDir;
    await this.s3Service
      .copyFileObject(sourceBucket, entityData.docPath, bucket, fileKey)
      .catch((error) => {
        this.logger.error(error, 'Failed copy s3 object ');
        throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
      });
    entityData.docPath = fileKey;
    const res = await this.lLMBackendService
      .genApiResource(entityData, llmModel.modelId, targetGroup.env)
      .catch((err) => {
        this.logger.error(err, 'PromoteApiResource failed');
        throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
      });
    entityData.generatedAt = res.status === ApiResourceStatus.GENERATED ? new Date() : null;
    entityData.groupId = targetGroup.id;
    entityData.status = res.status;
    entityData.supportCalls = res?.supportCalls ?? {};
    delete entityData.id;
    return entityData;
  }

  public async getHostMap(groupId: number, url: string): Promise<HostMap> {
    const hostList = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      ApiResourceService.HOST_MAP_FEATURE_FLAGS,
    );
    if (!hostList) {
      throw new ApiException(ErrorCode.CONNECT_API_FEATURE_FLAGS_HOST_LIST_NOT_EXISTS);
    }

    const hostMap = Object.values(JSON.parse(JSON.stringify(hostList.metaData))).find(
      (item) => item[ApiResourceService.HOST_MAP_TEST_KEY] === url,
    );
    if (!hostMap) {
      throw new ApiException(ErrorCode.CONNECT_API_FEATURE_FLAGS_HOST_LIST_NOT_EXISTS);
    }
    return hostMap as HostMap;
  }

  static featureFlagsHostListFormatValidator(
    mutilpleLevelFeatureFlagsModelDto: MutilpleLevelFeatureFlagsModelDto,
  ): void {
    const hostMap = mutilpleLevelFeatureFlagsModelDto.metaData;
    const hostListKeys = Object.keys(hostMap);
    if (!hostListKeys || hostListKeys.length === 0) {
      throw new ApiException(ErrorCode.CONNECT_API_FEATURE_FLAGS_HOST_MAP_NOT_EMPTY);
    }
    const verifiedHostList = [];
    hostListKeys.forEach((key) => {
      const host = hostMap[key];
      if (!(host instanceof Object)) {
        throw new ApiException(ErrorCode.CONNECT_API_FEATURE_FLAGS_HOST_MAP_INVALID);
      }
      if (
        !host[ApiResourceService.HOST_MAP_TEST_KEY] ||
        !host[ApiResourceService.HOST_MAP_PROD_KEY]
      ) {
        throw new ApiException(ErrorCode.CONNECT_API_FEATURE_FLAGS_HOST_VALUE_EMPTY);
      }

      if (Object.keys(host).length !== 2) {
        throw new ApiException(ErrorCode.CONNECT_API_FEATURE_FLAGS_HOST_VALUE_KEY_INVALID);
      }

      if (
        !isURL(host[ApiResourceService.HOST_MAP_TEST_KEY], ApiResourceService.IS_URL_OPTIONS) ||
        !isURL(host[ApiResourceService.HOST_MAP_PROD_KEY], ApiResourceService.IS_URL_OPTIONS)
      ) {
        throw new ApiException(ErrorCode.CONNECT_API_FEATURE_FLAGS_HOST_VALUE_NOT_URL);
      }

      if (
        verifiedHostList.includes(
          host[ApiResourceService.HOST_MAP_TEST_KEY],
          host[ApiResourceService.HOST_MAP_PROD_KEY],
        )
      ) {
        throw new ApiException(ErrorCode.CONNECT_API_FEATURE_FLAGS_HOST_URL_DUPLICATE);
      }

      verifiedHostList.push(
        host[ApiResourceService.HOST_MAP_TEST_KEY],
        host[ApiResourceService.HOST_MAP_PROD_KEY],
      );
    });
  }
}
