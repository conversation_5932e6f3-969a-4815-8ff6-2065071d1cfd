import {
  BadRequestException,
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  NotFoundException,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiProduces,
  ApiQuery,
  ApiResponse,
  ApiSecurity,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  FileEntityType,
  FileType,
  Group,
  GroupType,
  LLMModel,
  ModelFile,
  Prisma,
  ResourceSubsciberType,
} from '@prisma/client';
import { Response } from 'express';
import { ConvertSseInterceptor } from 'src/interceptors/convert-sse.interceptor';
import { ChatWithDataService } from 'src/providers/chat-with-data/chat-with-data.service';
import { GetSpecificChatflowResponse } from 'src/providers/flow-backend/flow-backend.interface';
import {
  CHAT_APPROACH,
  ChatResponse,
  EmbeddingsResponse,
  GenerateResponse,
  MODEL,
} from 'src/providers/llm-backend/llm-backend.interface';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { S3Service } from 'src/providers/s3/s3.service';
import { ExternalApi } from 'src/swagger-document';
import { v4 } from 'uuid';
import { ApiException, ErrorCode } from '../../../errors/errors.constants';
import { CursorPipe } from '../../../pipes/cursor.pipe';
import { OptionalIntPipe } from '../../../pipes/optional-int.pipe';
import { OrderByPipe } from '../../../pipes/order-by.pipe';
import { SelectIncludePipe } from '../../../pipes/select-include.pipe';
import { WherePipe } from '../../../pipes/where.pipe';
import { ElasticSearchService } from '../../../providers/elasticsearch/elasticsearch.service';
import { LLMBackendService } from '../../../providers/llm-backend/llm-backend.service';
import { Expose } from '../../../providers/prisma/prisma.interface';
import { ApiKeysService } from '../../api-keys/api-keys.service';
import { AuditLog } from '../../audit-logs/audit-log.decorator';
import { UserRequest } from '../../auth/auth.interface';
import { Scopes } from '../../auth/scope.decorator';
import { ChatHistoryInterceptor } from '../../chat-sessions/interceptor/chat-history.interceptor';
import { ChatRequestInterceptor } from '../../chat-sessions/interceptor/chat-request.interceptor';
import { FileHistoryService } from '../../file-history/file-history.service';
import {
  CreateFlowBotRequest,
  GetAvailableBotsForFlowResponse,
  GetFlowBotRequestResponse,
  RequestBotResponse,
  UpdateFlowBotRequestRequest,
  UpdateFlowBotRequestResponse,
} from '../../flow-bot-requests/flow-bot-requests.dto';
import { FlowBotRequestsService } from '../../flow-bot-requests/flow-bot-requests.service';
import { GetGroupFlowBotsResponse } from '../../flow-bots/flow-bots.dto';
import { FlowBotsService } from '../../flow-bots/flow-bots.service';
import {
  CreateGroupFlowRequest,
  CreateGroupFlowResponse,
  FlowChatRequest,
  FlowChatResponse,
  GetSpecificFlowResponse,
  UpdateGroupFlowRequest,
} from '../../flows/flows.dto';
import { FlowsService } from '../../flows/flows.service';
import {
  ChannelType,
  ChatLlmModelDto,
  FlowCallChatQuery,
  InternalChatLlmModelDto,
  LlmModelBasicInfoResponse,
} from '../../llm-models/dto/chat-llm-model.dto';
import { GenerateLlmModelDto } from '../../llm-models/dto/generate-llm-model.dto';
import { UpdateLlmModelDto } from '../../llm-models/dto/update-llm-model.dto';
import { UploadLlmModelFileDto } from '../../llm-models/dto/upload-llm-model-file.dto';
import { ChatResponseEntity } from '../../llm-models/entities/llm-model.entity';
import { LLMModelsService } from '../../llm-models/llm-models.service';
import { MembershipsService } from '../../memberships/memberships.service';
import { SummaryService } from '../../summary/summary.service';
import { UsersService } from '../../users/users.service';
import { ModelFileEntity } from '../entities/groups.entity';
import {
  AzureSpeechSttDto,
  AzureSpeechTtsDto,
  GroupNameResponseDto,
  PlanRequestDto,
  ProcessFileDto,
  ReplaceGroupDto,
  UpdateGroupDto,
} from '../groups.dto';
import { GroupsService } from '../groups.service';
import { PlansService } from 'src/modules/plans/plans.service';
import { Quota } from 'src/modules/auth/quota.decorator';
import { EmbeddingsDto } from '../../llm-models/dto/embeddings-llm-model.dto';
import { EmbeddingsResponseEntity } from '../../llm-models/entities/embeddings-llm-model.entity';
import { GenerateResponseEntity } from '../entities/generate-response.entity';
import { MessageEventDeltaEntity } from 'src/modules/llm-models/entities/llm-model.entity';
import * as DocTagDto from 'src/modules/document-tag/document-tag.dto';
import { DocumentTagService } from 'src/modules/document-tag/document-tag.service';
import { LlmEnginesService } from 'src/modules/llm-engines/llm-engines.service';
import { LLMModelsSettingService } from '../../llm-models/llm-models-setting.service';
import {
  LLMModelSettingResponse,
  UpdateLLMModelSettingDto,
} from 'src/modules/llm-models/dto/llm-model-setting.dto';
import { ChatRequestDataSourceInterceptor } from '../../chat-sessions/interceptor/chat-request-data-source.interceptor';
import { Readable } from 'stream';

@ApiSecurity('x-api-key')
@ApiBearerAuth('bearer-auth')
@ApiTags('Groups')
@Controller('groups')
export class GroupController {
  private logger = new Logger(GroupController.name);
  private uploadFileAllowExt = ['xlsx', 'csv', 'pdf'];
  constructor(
    private prisma: PrismaService,
    private groupsService: GroupsService,
    private llmModelService: LLMModelsService,
    private llmBackendService: LLMBackendService,
    private elasticSearchService: ElasticSearchService,
    private configService: ConfigService,
    private usersService: UsersService,
    private fileHistoryService: FileHistoryService,
    private s3Service: S3Service,
    private apiKeysService: ApiKeysService,
    private membershipsService: MembershipsService,
    private flowsService: FlowsService,
    private flowBotsService: FlowBotsService,
    private flowBotRequestsService: FlowBotRequestsService,
    private plansService: PlansService,
    private readonly summaryService: SummaryService,
    private readonly chatWithDataService: ChatWithDataService,
    private readonly documentTagService: DocumentTagService,
    private readonly llmEnginesService: LlmEnginesService,
    private readonly llmModelsSettingsService: LLMModelsSettingService,
  ) {}

  /** Get groups id and name */
  @Get('names')
  @Scopes('group-*:read-info')
  async getGroupsIdAndName(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: GroupNameResponseDto[] }> {
    const list = await this.groupsService.getGroupsNames(skip, take, where, orderBy);
    return { list };
  }

  /** Get group details */
  @Get(':groupId')
  @Scopes('group-{groupId}:read-info')
  async get(
    @Param('groupId', ParseIntPipe) id: number,
    @Query('select', SelectIncludePipe) select?: Record<string, boolean>,
    @Query('include', SelectIncludePipe) include?: Record<string, boolean>,
  ): Promise<Expose<Group>> {
    return this.groupsService.getGroup(id, { select, include });
  }

  /** Get group rate limit quota */
  @Get(':groupId/rate-limit')
  @Scopes('group-{groupId}:read-info', 'user-*:read-public-bot')
  async getRateLimitQuota(
    @Param('groupId', ParseIntPipe) id: number,
    @Query('channelType') channelType: ChannelType,
  ): Promise<{ points: number }> {
    return this.groupsService.getGroupRateLimitQuota(id, channelType);
  }

  /** Update a group */
  @Patch(':groupId')
  @AuditLog('update-info')
  @Scopes('group-{groupId}:write-info')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  @UseInterceptors(FileInterceptor('file'))
  async update(
    @UploadedFile() file: Express.Multer.File,
    @Body() data: UpdateGroupDto,
    @Param('groupId', ParseIntPipe) id: number,
  ): Promise<Expose<Group>> {
    // if group type is flow, then need to update flow name in flow backend database too
    const group = await this.groupsService.getGroup(id, {});

    if (GroupType.FLOW === GroupType[group.groupType] && data.name) {
      await this.flowsService.updateGroupFlowByGroupId(id, { name: String(data.name) });
    }
    data.profilePictureUrl = await this.groupsService.updateGroupAvatar(group, file, data);
    return this.groupsService.updateGroup(group, data);
  }

  /** Replace a group
   * @deprecated Frontend not supporting this function
   */
  @Put(':groupId')
  @AuditLog('update-info')
  @Scopes('group-{groupId}:write-info')
  async replace(
    @Body() data: ReplaceGroupDto,
    @Param('groupId', ParseIntPipe) id: number,
  ): Promise<Expose<Group>> {
    return this.groupsService.replaceGroup(id, { id, ...data });
  }

  @Get(':groupId/llm-model')
  @Scopes('group-{groupId}:read-llm-model')
  async getGroupLLMModel(@Param('groupId', ParseIntPipe) groupId: number): Promise<LLMModel> {
    return await this.llmModelService.findOneByGroupId(groupId);
  }

  @Get(':groupId/llm-model/setting')
  @Scopes('group-{groupId}:read-llm-model')
  async getGroupLLMModelSetting(
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<LLMModelSettingResponse> {
    return await this.llmModelsSettingsService.findOneByGroupId(groupId);
  }

  @Get(':groupId/llm-model-basic-info')
  @Scopes(`group-{groupId}:read-llm-model-basic`, `group-{groupId}:read-playground`)
  async getGroupLLMModelBasicInfo(
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<LlmModelBasicInfoResponse> {
    return await this.llmModelService.findBasicInfoByGroupId(groupId);
  }

  @Post(':groupId/llm-model/upload-temp-file')
  @Scopes('group-{groupId}:read-playground', 'user-*:read-playground')
  @UseInterceptors(FileInterceptor('file'))
  @UseInterceptors(ClassSerializerInterceptor)
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    required: true,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'file',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'OK',
  })
  async uploadTempFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @UploadedFile() file: Express.Multer.File,
    @Req() request: UserRequest,
  ) {
    if (!file) {
      throw new BadRequestException('no files');
    }

    try {
      const { user } = request;
      if (!user) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
      const model = await this.llmModelService.findOneByGroupId(groupId);
      if (!model) throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);

      const group = await this.llmModelService.getGroup(groupId);

      const fileUuid = v4();
      const fileLocation = await this.llmBackendService.uploadTempFile(
        group.env,
        fileUuid,
        file,
        groupId,
      );
      if (!fileLocation) {
        throw new BadRequestException('No respond from backend');
      }

      return { filename: fileLocation };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @HttpCode(HttpStatus.OK)
  @ExternalApi()
  @Post(':groupId/llm-model/azure-speech/tts')
  @ApiOperation({ summary: 'Text To Speech', description: '', tags: ['TTS'] })
  @ApiBody({
    type: AzureSpeechTtsDto,
    description: 'Azure Text to Speech',
    examples: {
      streaming: {
        summary: 'Text to speech (stream)',
        description: 'Azure Text to speech',
        value: {
          text: 'I go to school by bus.',
          voice_name: 'en-US-JennyNeural',
          stream: true,
        },
      },
      non_streaming: {
        summary: 'Text to speech',
        description: 'Azure Text to speech',
        value: {
          text: 'I go to school by bus.',
          voice_name: 'en-US-JennyNeural',
          stream: false,
        },
      },
    },
  })
  @ApiExtraModels(GenerateResponseEntity)
  @ApiResponse({
    status: 200,
    description: 'OK',
    content: {
      'application/json': {
        schema: {
          $ref: getSchemaPath(GenerateResponseEntity),
        },
        example: {
          data: {
            url: 'https://api.uat.bot-builder.pccw.com/v1/groups/...',
            file: '/100525../llm-model/generate/y5zs68m0w9....wav',
          },
          usage: {
            audioUsage: {
              chars_count: 95,
            },
          },
        },
      },
      'text/event-stream': {
        schema: {
          $ref: getSchemaPath(GenerateResponseEntity),
        },
        example:
          'event: overall\ndata: {"data": {"url": "https://api.uat.bot-builder.pccw.com/v1/groups/...", "file": "/100525../llm-model/generate/y5zs68m0w9....wav"}, "usage": {"audioUsage": {"chars_count": 95}}}\n\n',
      },
    },
  })
  @Scopes('group-{groupId}:read-playground')
  async azureSpeechTts(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() azureSpeechTtsDto: AzureSpeechTtsDto,
    @Req() request: UserRequest,
    @Res() response: Response,
  ): Promise<Response<GenerateResponse> | void> {
    const model = await this.llmModelService.findOneByGroupId(groupId);
    const group = await this.groupsService.getGroup(groupId, {});

    try {
      const llmRes = await this.llmBackendService.generateWithModel(
        group.id,
        group.env,
        model.modelId,
        {
          model: MODEL.AZURE_SPEECH,
          function: 'tts',
          overrides: azureSpeechTtsDto,
          groupId: groupId,
          userId: request.user?.id,
        },
        request,
        response,
      );

      if (!azureSpeechTtsDto?.stream) return response.json(llmRes);
    } catch (error) {
      this.logger.error(error, 'generate error');
      throw error;
    }
  }

  @HttpCode(HttpStatus.OK)
  @Post(':groupId/llm-model/azure-speech/stt')
  @ExternalApi()
  @Scopes('group-{groupId}:read-playground', 'user-*:read-playground')
  @ApiOperation({ summary: 'Speech To Text', description: '', tags: ['STT'] })
  @ApiBody({
    type: AzureSpeechSttDto,
    description: 'Azure Speech to Text',
    examples: {
      latest: {
        summary: 'Speech to Text',
        description: 'Azure Speech to Text',
        value: {
          files: [
            {
              data_source: 'upload',
              file_id: 's3Basename from upload chat file',
            },
          ],
          auto_detect_source_language: true,
          stream: true,
        },
      },
      old: {
        summary: 'Speech to Text (deprecated)',
        description: 'Azure Speech to Text',
        value: {
          file: 'upload temp file file id',
          auto_detect_source_language: true,
          stream: true,
        },
      },
    },
  })
  @ApiExtraModels(GenerateResponseEntity)
  @ApiResponse({
    status: 200,
    description: 'OK',
    content: {
      'application/json': {
        schema: {
          $ref: getSchemaPath(GenerateResponseEntity),
        },
        example: {
          data: {
            text: '想問下呢，有無見過呢個人啊...',
          },
          usage: {
            audioUsage: {
              file_length: 2.268,
            },
          },
        },
      },
      'text/event-stream': {
        schema: {
          $ref: getSchemaPath(GenerateResponseEntity),
        },
        example:
          'event: overall\ndata: {"data": {"text": "想問下呢，有無見過呢個人啊..."}, "usage": {"audioUsage":{"file_length": 2.268}}}\n\n',
      },
    },
  })
  async azureSpeechStt(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() azureSpeechSttDto: AzureSpeechSttDto,
    @Req() request: UserRequest,
    @Res() response: Response,
  ): Promise<Response<GenerateResponse> | void> {
    const model = await this.llmModelService.findOneByGroupId(groupId);
    const group = await this.groupsService.getGroup(groupId, {});
    if (request.user.type == 'user') {
      await this.llmModelService.checkUserHasPermissionsOrIsPublicBot(request, model, group.env);
    }
    try {
      let s3_key = '';
      if (azureSpeechSttDto.file != null) {
        s3_key = `generated-resources/temporary/${azureSpeechSttDto.file}`;
      } else if (azureSpeechSttDto.files != null) {
        const chatFiles = await this.llmModelService.collectS3Paths(
          groupId,
          request.user?.id,
          azureSpeechSttDto.files,
        );
        if (chatFiles && chatFiles.length > 0) {
          s3_key = 's3_key' in chatFiles[0] ? chatFiles[0]['s3_key'].toString() : '';
        }
      }

      if (s3_key === '') {
        throw new ApiException(ErrorCode.FIlE_NOT_FOUND);
      }

      const llmRes = await this.llmBackendService.generateWithModel(
        group.id,
        group.env,
        model.modelId,
        {
          model: MODEL.AZURE_SPEECH,
          function: 'stt',
          overrides: {
            ...azureSpeechSttDto,
            s3_key: s3_key,
          },
        },
        request,
        response,
      );

      if (!azureSpeechSttDto?.stream) return response.json(llmRes);
    } catch (error) {
      this.logger.error(error, 'generate error');
      throw error;
    }
  }

  @HttpCode(HttpStatus.OK)
  @Post(':groupId/llm-model/generate')
  @Scopes('group-{groupId}:read-playground')
  async generateGroupLLMModel(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() generateLlmModelDto: GenerateLlmModelDto,
    @Req() request: UserRequest,
    @Res() response: Response,
  ): Promise<Response<GenerateResponse> | void> {
    const model = await this.llmModelService.findOneByGroupId(groupId);
    const group = await this.groupsService.getGroup(groupId, {});

    const llmRes = await this.llmBackendService.generateWithModel(
      group.id,
      group.env,
      model.modelId,
      generateLlmModelDto,
      request,
      response,
    );

    if (!generateLlmModelDto.overrides?.stream) return response.json(llmRes);
  }

  @Get(':groupId/llm-model/generate/:filename')
  @Scopes('group-{groupId}:read-playground')
  @AuditLog('download-temp-generated-file')
  async downloadGeneratedContent(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('filename') filename: string,
    @Res() response: Response,
  ) {
    
      const group = await this.groupsService.getGroup(groupId, {});

      const fileResponse = await this.s3Service.getObjectUnsafe(
        this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`),
        `generated-resources/temporary/{filename}`,
      );

      (fileResponse.Body as Readable)
      .on('error', function () {
        response.status(404).json({ statusCode: 404, message: 'Not Found' });
      })
      .pipe(response);
  }

  @HttpCode(HttpStatus.OK)
  @Post(':groupId/llm-model/chat')
  @ExternalApi()
  @Scopes('group-{groupId}:read-playground', 'group-{groupId}:read-flow-playground')
  @ApiOperation({ summary: 'Chat', description: '', tags: ['Chat'] })
  @ApiExtraModels(MessageEventDeltaEntity)
  @ApiBody({
    type: ChatLlmModelDto,
    description: 'Chat',
    examples: {
      Part1_simple_chat: {
        summary: 'Simple Chat',
        description: 'Simple Chat',
        value: {
          approach: 'rrr',
          history: [
            {
              role: 'user',
              content: 'tell me a joke',
            },
          ],
          overrides: {
            top: 0,
            model: 'gpt-4o-mini',
            max_tokens: 1000,
            temperature: 0,
            top_p: 1,
            presence_penalty: 0,
            frequency_penalty: 0,
            show_reference: false,
            stream: false,
          },
        },
      },
      Part2_tools_calling_1: {
        summary: 'Tools Calling #1',
        description: 'Tools Calling #1',
        value: {
          approach: 'rrr',
          history: [
            {
              role: 'user',
              content: '想問下美孚富臨幾多號電話？',
            },
          ],
          overrides: {
            top: 0,
            model: 'gpt-4o-mini',
            max_tokens: 1000,
            temperature: 0,
            top_p: 1,
            presence_penalty: 0,
            frequency_penalty: 0,
            tools: [
              {
                type: 'function',
                function: {
                  name: '1083_hotline',
                  description: 'extract keywords based on prompt',
                  parameters: {
                    type: 'object',
                    properties: {
                      address: {
                        type: 'string',
                        description: 'The address',
                      },
                      biz_name: {
                        type: 'string',
                        description: 'The business name and branch or district',
                      },
                      category: {
                        type: 'string',
                        enum: [
                          'government department',
                          'restaurant',
                          'educational institutions',
                          'hotel / airline / travel agent',
                          'other',
                        ],
                      },
                      region: {
                        type: 'string',
                        enum: ['hong kong island', 'kowloon', 'new territorie'],
                      },
                    },
                    required: ['biz_name', 'category'],
                  },
                },
              },
            ],
            stream: false,
          },
        },
      },
      Part2_tools_calling_2: {
        summary: 'Tools Calling #2',
        description: 'Tools Calling #2',
        value: {
          approach: 'rrr',
          history: [
            {
              role: 'user',
              content: '想問下美孚富臨幾多號電話？',
            },
            {
              role: 'assistant',
              tool_calls: [
                {
                  id: 'call_ljL3ATufrlEB3q15nkh6GFqS',
                  function: {
                    arguments: '{"biz_name":"美孚富臨","category":"others"}',
                    name: '1083_hotline',
                  },
                  type: 'function',
                },
              ],
            },
            {
              role: 'tool',
              tool_call_id: 'call_ljL3ATufrlEB3q15nkh6GFqS',
              name: '1083_hotline',
              content: '{ 電話號碼: 23683738, 公司名稱: 富臨皇宮, 地址: 荔枝角百老匯街美孚新邨}',
            },
          ],
          overrides: {
            top: 0,
            model: 'gpt-4o-mini',
            max_tokens: 1000,
            temperature: 0,
            top_p: 1,
            presence_penalty: 0,
            frequency_penalty: 0,
            stream: false,
          },
        },
      },
      Part3_chat_with_file_2: {
        summary: 'Chat with file #2',
        description: 'Chat with file #2',
        value: {
          approach: 'cwf',
          history: [
            {
              role: 'user',
              content: 'what do you find in the file',
            },
          ],
          overrides: {
            top: 0,
            model: 'vertexai-gemini-1.5-flash-001',
            max_tokens: 1000,
            temperature: 0,
            top_p: 0.95,
            presence_penalty: 0,
            frequency_penalty: 0,
            stream: false,
          },
          files: [
            {
              data_source: 'upload',
              file_id: '5d461366-5f3e-4701-9dc5-aafc6f61f63d.jpg',
            },
          ],
        },
      },
      Part4_image_generation: {
        summary: 'image generation',
        description: 'image generation',
        value: {
          approach: 'rrr',
          history: [
            {
              role: 'user',
              content:
                'draw a quick siliver color tesla model y with license plate "HKT" which stopped in front of traffic lamp in Nathan Road, Hong Kong. Next lane is a yellow porsche 911 with license plate "CSL".',
            },
          ],
          overrides: {
            model: 'dalle-3',
            size: '1024x1024',
            quality: 'standard',
            stream: true,
          },
        },
      },
      Part5_genkb: {
        summary: 'GenKB Chat',
        description: 'GenKB Chat',
        value: {
          approach: 'rrr',
          history: [
            {
              role: 'user',
              content: 'Show me CSL 5G plan',
            },
          ],
          overrides: {
            model: 'gpt-4o-mini',
            genkb: true,
            genkb_top: 3,
            genkb_params: [
              {
                kb_id: 10360135,
                filter: {
                  content_group_ids: [],
                  types: ['PAGE', 'FILE', 'TAG'],
                  tags: ['CSL'],
                  mandatory_tags: [],
                },
              },
            ],
            stream: true,
          },
        },
      },
      Part6_imageChat: {
        summary: 'Image Chat',
        description: 'Image Chat',
        value: {
          approach: 'rrr',
          history: [
            {
              role: 'user',
              content: [
                {
                  'type': 'text',
                  'text': 'describe the image'
                },
                {
                  'type': 'image_url',
                  'image_url': {
                    'url': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQCEN1BTMO0CxumzqGv6PmZciI9iUKv0aUFtA&s'
                  },
                }
              ]
            },
          ],
          overrides: {
            top: 0,
            genkb: false,
            model: 'gpt-4o-mini',
            max_tokens: 1000,
            temperature: 0,
            top_p: 1,
            stream: true,
          },
        },
      },
    },
  })
  @ApiExtraModels(ChatResponseEntity)
  @ApiResponse({
    status: 200,
    description: 'OK',
    content: {
      'application/json': {
        schema: {
          $ref: getSchemaPath(ChatResponseEntity),
        },
        examples: {
          chat_response: {
            summary: 'simple chat response',
            description: 'simple chat response',
            value: {
              suggestReplies: '',
              answer:
                'Why did the scarecrow win an award?\n\nBecause he was outstanding in his field!',
              tool_calls: null,
              content_points: null,
              thoughts: 'Searched for:\nPrompt: - \ntell me a joke',
              message: {
                content:
                  'Why did the scarecrow win an award?\n\nBecause he was outstanding in his field!',
                role: 'assistant',
              },
              usage: {
                promptTokens: 17,
                completionTokens: 17,
                totalCompletionTokens: 34,
                embeddingTokens: 0,
              },
              content_filter_results: null,
            },
          },
          image_response: {
            summary: 'image generation response',
            description: 'image generation response',
            value: {
              answer: '![image](https://api.uat.bot-builder.pccw.com/v1/groups/...)',
              message: {
                role: 'assistant',
                content: '![image](https://api.uat.bot-builder.pccw.com/v1/groups/...)',
              },
              usage: {
                embeddingTokens: 0,
                promptTokens: 0,
                completionTokens: 0,
                totalCompletionTokens: 0,
                imageUsage: {
                  quality: 'standard',
                  resolution: '1024x1024',
                  feature: null,
                },
              },
              content_points: null,
              safety_ratings: null,
              web_search_points: null,
              file_expired_at: '2024-10-12T17:21:14.195Z',
            },
          },
        },
      },
      'text/event-stream': {
        schema: {
          $ref: getSchemaPath(MessageEventDeltaEntity),
        },
        examples: {
          chat_response: {
            summary: 'simple chat streaming response',
            description: 'simple chat streaming response',
            value:
              'event: connect\ndata: {"connect": true}\n\nevent: message\ndata: {"delta": {"content": "Why", "function_call": null, "role": "assistant", "tool_calls": null}, "finish_reason": null, "index": 0, "logprobs": null, "content_filter_results": {"hate": {"filtered": false, "severity": "safe"}, "self_harm": {"filtered": false, "severity": "safe"}, "sexual": {"filtered": false, "severity": "safe"}, "violence": {"filtered": false, "severity": "safe"}}}\n\nevent: overall\ndata: {"answer": "Why did the scarecrow win an award?\n\nBecause he was outstanding in his field!", "message": {"role": "assistant", "content": "Why did the scarecrow win an award?\n\nBecause he was outstanding in his field!"}, "usage": {"embeddingTokens": 0, "promptTokens": 16, "completionTokens": 23, "totalCompletionTokens": 39, "imageUsage": null}, "content_points": null, "safety_ratings": null, "web_search_points": null}',
          },
          image_response: {
            summary: 'image generation streaming response',
            description: 'image generation response',
            value:
              'event: connect\ndata: {"connect": true}\n\nevent: overall\ndata: {"answer":"![image](https://api.uat.bot-builder.pccw.com/v1/groups/...)","message":{"role":"assistant","content":"![image](https://api.dev.bot-builder.pccw.com/v1/groups/...)"},"usage":{"embeddingTokens":0,"promptTokens":0,"completionTokens":0,"totalCompletionTokens":0,"imageUsage":{"quality":"standard","resolution":"1024x1024","feature":null}},"content_points":null,"safety_ratings":null,"web_search_points":null,"file_expired_at":"2024-10-12T17:12:17.062Z"}',
          },
        },
      },
    },
  })
  @ApiQuery({ name: 'flowId', required: false, type: Number })
  @Quota(
    'group-{groupId}:llm-engine-{llmEngine}-quota',
    'group-{groupId}:rate-limit-{channel}-quota',
  )
  @UseInterceptors(
    ChatRequestInterceptor,
    ChatHistoryInterceptor,
    ChatRequestDataSourceInterceptor,
    ConvertSseInterceptor,
  )
  async chatGroupLLMModel(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() chatLlmModelDto: ChatLlmModelDto,
    @Req() request: UserRequest,
    @Res() res: Response,
    @Query() flowQuery?: FlowCallChatQuery,
  ): Promise<Response<ChatResponse> | void> {
    const group = await this.groupsService.getGroup(groupId, {});
    const isStream: boolean = chatLlmModelDto.overrides?.stream;
    chatLlmModelDto.groupId = groupId;
    chatLlmModelDto.userId = request.user?.id;
    let llmRes;
    if (chatLlmModelDto.approach === CHAT_APPROACH.CWD) {
      llmRes = await this.chatWithDataService.chatWithData(group, request, chatLlmModelDto, res);
    } else {
      llmRes = await this.llmModelService.chatWithLLM(
        group,
        request,
        chatLlmModelDto,
        res,
        flowQuery,
      );
    }
    if (!isStream) return res.json(llmRes);
  }

  @Post(':groupId/llm-model/internal/chat')
  @Scopes(
    'group-{groupId}:read-playground-internal',
    'group-{groupId}:read-flow-playground-internal',
    'group-*:read-playground-internal',
    'group-*:read-flow-playground-internal',
  )
  @UseInterceptors(ChatRequestInterceptor, ChatRequestDataSourceInterceptor, ConvertSseInterceptor)
  async internalChatGroupLLMModel(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() chatLlmModelDto: InternalChatLlmModelDto,
    @Req() request: UserRequest,
    @Res() res: Response,
    @Query() flowQuery?: FlowCallChatQuery,
  ) {
    const isStream: boolean = chatLlmModelDto.overrides?.stream;
    if (chatLlmModelDto?.internalCallRequesterId) {
      request.user.id = chatLlmModelDto.internalCallRequesterId;
      delete chatLlmModelDto.internalCallRequesterId;
    }
    if (chatLlmModelDto?.isTokenLimitCheckNeed) {
      await this.llmEnginesService.checkChatTokenLimit(groupId, [chatLlmModelDto.overrides.model]);
      delete chatLlmModelDto.isTokenLimitCheckNeed;
    }

    const llmRes = await this.chatGroupLLMModel(groupId, chatLlmModelDto, request, res, flowQuery);
    if (!isStream) return llmRes;
  }

  @Get(':groupId/llm-model/upload-file')
  @Scopes('group-{groupId}:read-llm-model-files')
  @UseInterceptors(ClassSerializerInterceptor)
  @ApiOperation({ summary: 'List uploaded files', tags: ['File Management'] })
  @ApiQuery({ name: 'skip', required: false, type: Number })
  @ApiQuery({ name: 'take', required: false, type: Number })
  @ApiResponse({
    status: 200,
    description: 'OK',
    type: [ModelFileEntity],
  })
  async getFiles(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('cursor', CursorPipe) cursor?: Record<string, number | string>,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: ModelFileEntity[]; count: number }> {
    const model = await this.llmModelService.findOneByGroupId(groupId);
    if (!model) throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);
    const rst = await this.llmModelService.findModelFiles({
      skip,
      take,
      orderBy,
      where: { ...where, groupId },
    });
    const count = await this.llmModelService.getModelFilesCount({ ...where, groupId });
    const list = rst.map((item) => new ModelFileEntity(item));
    return { list, count };
  }

  @Patch(':groupId/llm-model')
  @Scopes('group-{groupId}:write-llm-model')
  async updateLlmModel(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() updateLlmModelDto: UpdateLlmModelDto,
    @Req() request: UserRequest,
  ) {
    const { user } = request;
    const updatedLLMModel = this.prisma.$transaction(async (tx) => {
      const LLMModel = await this.llmModelService.update(
        updateLlmModelDto.modelId,
        updateLlmModelDto.tone,
        updateLlmModelDto.startupMessage,
        updateLlmModelDto.llmEngineId,
        updateLlmModelDto.typeDefinition,
        updateLlmModelDto.showReference ?? true,
        user.id,
        updateLlmModelDto.parameters as object,
        tx,
        updateLlmModelDto?.messageTemplateId,
      );
      return LLMModel;
    });
    return updatedLLMModel;
  }

  @Patch(':groupId/llm-model/setting')
  @Scopes('group-{groupId}:write-llm-model')
  async updateLlmModelSetting(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() updateLLMModelSettingDto: UpdateLLMModelSettingDto,
    @Req() request: UserRequest,
  ) {
    const { user } = request;
    return this.llmModelsSettingsService.update(
      updateLLMModelSettingDto.modelId,
      user.id,
      updateLLMModelSettingDto.active,
      updateLLMModelSettingDto.showInTeams,
      updateLLMModelSettingDto.showRefInTeams,
      updateLLMModelSettingDto.makeLiveToPublic,
      updateLLMModelSettingDto.canShareChat,
    );
  }

  @Post(':groupId/llm-model/upload-file')
  @AuditLog('upload-file')
  @Scopes('group-{groupId}:upload-llm-model-files')
  @UseInterceptors(FilesInterceptor('file'))
  @UseInterceptors(ClassSerializerInterceptor)
  @ApiOperation({ summary: 'Uploaded file', tags: ['File Management'] })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    required: true,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'file',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'OK',
    type: ModelFileEntity,
  })
  async uploadFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @UploadedFiles() files: Array<Express.Multer.File>,
    @Req() request: UserRequest,
    @Body() body: UploadLlmModelFileDto,
  ) {
    if (files && files.length) {
      const fileExtension = files[0].originalname.split('.').pop();
      if (!this.uploadFileAllowExt.includes(fileExtension.toLowerCase())) {
        throw new ApiException(ErrorCode.INVALID_FILE_EXTENSION);
      }
      try {
        const { user } = request;
        if (!user) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
        let userId = user.id;
        // currently api key upload file just will by the postman monitor call,
        // if the api support api key to upload file need hanlde it . currently just default count to the GroupOwner.
        if (user.type == 'api-key') {
          const membership = await this.membershipsService.getGroupOwnerMembership(groupId);
          userId = membership.userId;
        }
        await this.llmModelService.checkExceedPreUserPreDayUploadLimit(groupId, userId);
        const model = await this.llmModelService.findOneByGroupId(groupId);
        const fileUuid = v4();
        if (!model) throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);

        const group = await this.llmModelService.getGroup(groupId);

        const { s3Path: fileLocation, size } = await this.llmBackendService.uploadModelFile(
          group.env,
          model.modelId,
          fileUuid,
          files[0],
          groupId,
        );
        if (!fileLocation) {
          throw new BadRequestException('No respond from backend');
        }

        const doc = await this.llmModelService.updateModelFile(
          model,
          userId,
          files[0],
          fileUuid,
          fileLocation,
          size,
          body.fileClassification,
          body.autoIndex,
        );

        this.logger.log({
          Action: 'Going to data processor to verify model file',
          'group.env': group.env,
          'model.modelId': model.modelId,
          'doc.s3Path': doc.s3Path,
          groupId: groupId,
          userId: userId,
        });
        //scan malware
        this.llmModelService
          .modelFileMalwareScan(doc)
          .catch((error: Error) =>
            this.logger.error(`modelFileMalwareScan ${error.message}`, error.stack),
          );

        return new ModelFileEntity(doc);
      } catch (error) {
        this.logger.error(error);
        throw error;
      }
    } else {
      throw new BadRequestException('no files');
    }
  }

  @Get(':groupId/llm-model/reviewer-count')
  @Scopes('group-{groupId}:read-llm-model-files')
  async getBotReviewerCount(@Param('groupId', ParseIntPipe) groupId: number) {
    const reviewerCount = await this.llmModelService.getBotReviewerCount(groupId);
    return { count: reviewerCount };
  }

  @Get(':groupId/llm-model/upload-file/tagging-prompts')
  @Scopes('group-{groupId}:read-llm-model-files')
  @ApiOperation({
    summary: 'Get all file tagging prompt in history in group',
    tags: ['File Management'],
  })
  async getGroupTaggingPrompt(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('take', OptionalIntPipe) take: number = 10,
    @Query('skip', OptionalIntPipe) skip: number = 0,
  ) {
    const [count, prompts] = await Promise.all([
      this.documentTagService.countPromptsByGroup(groupId),
      this.documentTagService.getPromptsByGroup(groupId, { take, skip }),
    ]);
    return { count, prompts };
  }

  @Get(':groupId/llm-model/upload-file/tags')
  @Scopes('group-{groupId}:read-llm-model-files')
  @ApiOperation({ summary: 'Get all file tags in group', tags: ['File Management'] })
  async getGroupTags(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('take', OptionalIntPipe) take: number = 30,
    @Query('skip', OptionalIntPipe) skip: number = 0,
    @Query('orderBy', OrderByPipe) orderBy: Prisma.LabelsFindManyArgs['orderBy'] = { name: 'asc' },
  ) {
    const [count, tags] = await Promise.all([
      this.documentTagService.countTagsByGroup(groupId),
      this.documentTagService.getTagsByGroup(groupId, { orderBy, take, skip }),
    ]);
    return { count, tags };
  }

  @Post(':groupId/llm-model/upload-file/bulk/tags')
  @Scopes('group-{groupId}:write-llm-model-files-tags', 'group-*:write-llm-model-files-tags')
  @AuditLog('bulk-create-model-files-tags')
  @ApiOperation({ summary: 'Create tags in multiple files', tags: ['File Management'] })
  @ApiCreatedResponse({ type: DocTagDto.DocumentTagBulkCreateResponse })
  @ApiBadRequestResponse()
  @ApiInternalServerErrorResponse()
  async bulkCreateDocTags(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() body: DocTagDto.DocumentTagBulkCreateInput,
  ) {
    this.logger.debug(
      `POST :groupId/llm-model/upload-file/bulk/tags, input: ${JSON.stringify({
        groupId,
        body,
        user: req.user,
      })}`,
    );
    return this.documentTagService.bulkCreateTags(groupId, body, req.user, 'add');
  }

  @Put(':groupId/llm-model/upload-file/bulk/tags')
  @Scopes('group-{groupId}:write-llm-model-files-tags', 'group-*:write-llm-model-files-tags')
  @AuditLog('bulk-set-model-files-tags')
  @ApiOperation({ summary: 'Set tags in multiple files', tags: ['File Management'] })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ type: DocTagDto.DocumentTagBulkCreateResponse })
  @ApiBadRequestResponse()
  @ApiInternalServerErrorResponse()
  async bulkReplaceDocTags(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() body: DocTagDto.DocumentTagBulkCreateInput,
  ) {
    this.logger.debug(
      `PUT :groupId/llm-model/upload-file/bulk/tags, input: ${JSON.stringify({
        groupId,
        body,
        user: req.user,
      })}`,
    );
    return this.documentTagService.bulkCreateTags(groupId, body, req.user, 'replace');
  }

  @Delete(':groupId/llm-model/upload-file/bulk/tags/all')
  @Scopes('group-{groupId}:write-llm-model-files-tags')
  @AuditLog('bulk-delete-model-files-all-tags')
  @ApiOperation({ summary: 'Delete all tags in multiple files', tags: ['File Management'] })
  @HttpCode(HttpStatus.OK)
  @ApiBadRequestResponse()
  @ApiInternalServerErrorResponse()
  async bulkDeleteAllDocTags(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() body: DocTagDto.DocumentTagBulkDeleteAllInput,
  ) {
    this.logger.debug(
      `DELETE :groupId/llm-model/upload-file/bulk/tags/all, input: ${JSON.stringify({
        groupId,
        body,
        user: req.user,
      })}`,
    );
    await this.documentTagService.bulkDeleteAllTags(groupId, body);
    return { success: true };
  }

  @Post(':groupId/llm-model/upload-file/bulk/tags/suggest')
  @Scopes('group-{groupId}:read-llm-model-files')
  @AuditLog('bulk-suggest-model-files-tags')
  @ApiOperation({ summary: 'Suggest tags in multiple files', tags: ['File Management'] })
  @HttpCode(HttpStatus.OK)
  @ApiBadRequestResponse()
  @ApiInternalServerErrorResponse()
  async bulkSuggestDocTags(
    @Req() req: UserRequest,
    @Res() res: Response,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() body: DocTagDto.DocumentTagBulkSuggestInput,
  ) {
    this.logger.debug(
      `POST :groupId/llm-model/upload-file/bulk/tags/suggest, input: ${JSON.stringify({
        groupId,
        body,
        user: req.user,
      })}`,
    );

    if (!body.stream) {
      try {
        res.json(await this.documentTagService.bulkSuggestTags(groupId, body, req));
      } catch (e) {
        this.logger.error(e);
        if (e instanceof ApiException) throw e;
        throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR, { error: e });
      }
    } else {
      try {
        res.set({
          'Cache-Control': 'no-cache',
          Connection: 'keep-alive',
          'Content-Type': 'text/event-stream',
        });
        res.flushHeaders();
        res.write(
          this.documentTagService.formatSSE({
            event: 'connect',
            data: JSON.stringify({ connect: true }),
          }),
        );
        await this.documentTagService.bulkSuggestTags(groupId, body, req, res);
      } catch (e) {
        this.logger.error(e);
        let error = e;
        let data: string;
        if (e instanceof ApiException) data = JSON.stringify(e.getResponse());
        else {
          data = JSON.stringify(e);
          error = new ApiException(ErrorCode.INTERNAL_SERVER_ERROR, { error: e });
        }
        res.write(this.documentTagService.formatSSE({ event: 'error', data }));
        throw error;
      } finally {
        res.end();
      }
    }
  }

  @Delete(':groupId/llm-model/upload-file/:fileId/tags/:tagId')
  @Scopes('group-{groupId}:write-llm-model-files-tags')
  @AuditLog('delete-model-files-tags')
  @ApiOperation({ summary: 'Delete a file tag', tags: ['File Management'] })
  @HttpCode(HttpStatus.OK)
  @ApiBadRequestResponse()
  @ApiInternalServerErrorResponse()
  async deleteDocTags(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('fileId', ParseIntPipe) fileId: number,
    @Param('tagId', ParseIntPipe) tagId: number,
  ) {
    this.logger.debug(
      `DELETE :groupId/llm-model/upload-file/:fileId/tags/tagId, input: ${JSON.stringify({
        groupId,
        fileId,
        tagId,
        user: req.user,
      })}`,
    );
    await this.documentTagService.deleteTag(groupId, fileId, tagId, req.user);
    return { success: true };
  }

  @Get(':groupId/llm-model/upload-file/:fileId/info')
  @Scopes('group-{groupId}:read-llm-model-files')
  @UseInterceptors(ClassSerializerInterceptor)
  @ApiOperation({ summary: 'Get uploaded file information', tags: ['File Management'] })
  @ApiResponse({
    status: 200,
    description: 'OK',
    type: [ModelFileEntity],
  })
  async getFileByFileId(@Param('fileId', ParseIntPipe) fileId: number): Promise<ModelFile> {
    const model = await this.llmModelService.findModelFile(fileId);
    return model;
  }

  @Post(':groupId/llm-model/approve-or-process-file')
  @Scopes('group-{groupId}:approve-or-process-llm-model-files')
  // This AuditLog event would be special handled and translate to: approve-file, approve-and-process-file
  @AuditLog('approve-or-process-file')
  async approveOrProcessFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: ProcessFileDto,
  ): Promise<ModelFileEntity> {
    const group = await this.groupsService.getGroup(groupId, {});
    const doc = await this.llmModelService.approveOrProcessModelFile(group, data.docId, false);
    return new ModelFileEntity(doc);
  }

  @Post(':groupId/llm-model/process-file')
  @Scopes('group-{groupId}:process-llm-model-files')
  @UseInterceptors(ClassSerializerInterceptor)
  @ApiOperation({
    summary: 'Process file, only applicable when file status = VERIFY_SUCCESS',
    tags: ['File Management'],
  })
  @ApiResponse({
    status: 201,
    description: 'OK',
    type: ModelFileEntity,
  })
  @AuditLog('final-approve-and-process-file')
  async processFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: ProcessFileDto,
  ): Promise<ModelFileEntity> {
    try {
      const group = await this.groupsService.getGroup(groupId, {});
      const doc = await this.llmModelService.approveOrProcessModelFile(group, data.docId, true);
      return new ModelFileEntity(doc);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Get(':groupId/llm-model/upload-file/:docId')
  @Scopes('group-{groupId}:download-llm-model-files')
  @AuditLog('download-model-file')
  @ApiOperation({ summary: 'Download file', tags: ['File Management'] })
  @ApiProduces(
    'application/pdf',
    'text/csv',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  )
  @ApiResponse({
    status: 200,
    description: 'OK',
    schema: {
      type: 'file',
      format: 'binary',
    },
  })
  async downloadFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('docId') docId: string,
    @Res() res: Response,
  ) {
    try {
      const model = await this.llmModelService.findOneByGroupId(groupId);
      if (!model) throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);

      const file = await this.llmModelService.getModelFileInfoByDocId(docId);
      if (!file) throw new NotFoundException('File cannot be found in DB');

      await this.llmModelService.getModelFileByDocId(file, model, res);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Delete(':groupId/llm-model/upload-file/:docId')
  @Scopes('group-{groupId}:delete-llm-model-files')
  @UseInterceptors(ClassSerializerInterceptor)
  @ApiOperation({ summary: 'Delete file', tags: ['File Management'] })
  @ApiResponse({
    status: 200,
    description: 'OK',
    type: ModelFileEntity,
  })
  async deleteFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('docId') docId: string,
    @Req() request: UserRequest,
  ) {
    try {
      this.logger.warn('haha, is this Pino?');
      const model = await this.llmModelService.findOneByGroupId(groupId);
      if (!model) throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);
      const doc = await this.llmModelService.deleteModelFileByDocId(model, docId, request);
      return new ModelFileEntity(doc);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Get(':groupId/flow')
  @Scopes('group-{groupId}:read-flow')
  async getSpecificFlowByGroupId(
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<GetSpecificFlowResponse> {
    return await this.flowsService.getSpecificFlowByGroupId(groupId);
  }

  @Get(':groupId/logfile/list')
  @Scopes('group-{groupId}:export-logs')
  async listLogFile(
    @Param('groupId', ParseIntPipe) botId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    try {
      const newWhere = {
        ...where,
        entityId: botId,
        entityType: FileEntityType.BOT,
        fileType: FileType.QUERY_LOG,
      };
      const list = await this.fileHistoryService.list(orderBy, skip, take, newWhere);
      const count = await this.fileHistoryService.getCount(newWhere);
      return {
        list,
        count,
      };
    } catch (err) {
      this.logger.error(err, 'list log file error ');
      throw new ApiException(ErrorCode.LIST_FILE_DOWNLOAD_FAILED);
    }
  }

  @Post(':groupId/flow/chat')
  @ExternalApi()
  @Scopes('group-{groupId}:read-flow-playground')
  @UseInterceptors(ConvertSseInterceptor, ChatRequestInterceptor)
  @ApiOperation({
    summary: 'Chat with Flow',
    description: 'chat flow',
    tags: ['Chat'],
    operationId: 'chatFlow',
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
    type: FlowChatResponse,
  })
  async chatWithFlow(
    @Req() req: UserRequest,
    @Res() res: Response,
    @Param('groupId', ParseIntPipe) groupId: number, // flowId
    @Body() data: FlowChatRequest,
  ): Promise<FlowChatResponse | Response<FlowChatResponse>> {
    return await this.flowsService.chat(req, res, groupId, data);
  }

  // Flowise ACL
  @Get(':groupId/flows/:flowUuid')
  @Scopes('group-{groupId}:read-flow')
  async getSpecificGroupFlow(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('flowUuid') flowUuid: string,
  ): Promise<GetSpecificChatflowResponse> {
    return await this.flowsService.getSpecificFlow(groupId, flowUuid);
  }

  @Post(':groupId/flows')
  @Scopes('group-{groupId}:write-flow')
  async createGroupFlow(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: CreateGroupFlowRequest,
  ): Promise<CreateGroupFlowResponse> {
    return this.flowsService.createGroupFlow(groupId, data);
  }

  // Flowise ACL
  @Put(':groupId/flows/:flowUuid')
  @Scopes('group-{groupId}:write-flow')
  async updateGroupFlow(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('flowUuid') flowUuid: string,
    @Body() data: UpdateGroupFlowRequest,
  ): Promise<GetSpecificChatflowResponse> {
    return this.flowsService.updateGroupFlow(groupId, flowUuid, data);
  }

  // For Connected Flows / Bots page to get ACTIVE flowBot records
  @Get(':groupId/flow-bots')
  @Scopes('group-{groupId}:read-flow')
  async getGroupFlowBots(
    @Param('groupId', ParseIntPipe) groupId: number, //  group id of flow
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: GetGroupFlowBotsResponse[]; count: number }> {
    return await this.flowBotsService.getFlowBotsForGroup(groupId, skip, take, where, orderBy);
  }

  @Get(':groupId/flow-bot-requests/available-bots')
  @Scopes('group-{groupId}:write-flow')
  async getAvailableBotsForFlow(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number, //  group id of flow
  ): Promise<{ list: GetAvailableBotsForFlowResponse[]; count: number }> {
    return await this.flowBotRequestsService.getAvailableBotsForFlow(req.user.id, groupId);
  }

  // Delete flow bot connection from flow / bot page
  @Delete(':groupId/flow-bots/:flowBotId')
  @AuditLog('delete-flow-bot')
  @Scopes('group-{groupId}:delete-flow-bot')
  async deleteFlowBot(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number, //  group id of bot / flow
    @Param('flowBotId', ParseIntPipe) flowBotId: number,
  ): Promise<{ flowBotId: number }> {
    return this.flowBotsService.deleteFlowBot(req.user.id, groupId, flowBotId);
  }

  // Create flow bot request
  @Post(':groupId/flow-bot-requests')
  @Scopes('group-{groupId}:write-flow')
  async createFlowBotRequest(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number, // group id of flow
    @Body() data: CreateFlowBotRequest,
  ): Promise<RequestBotResponse> {
    return await this.flowBotRequestsService.createFlowBotRequest(req.user.id, groupId, data);
  }

  @Get(':groupId/flow-bot-requests')
  @Scopes('group-{groupId}:read-flow-bot-request')
  async getFlowBotRequest(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number, // group id of bot / flow
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: GetFlowBotRequestResponse[]; count: number }> {
    return await this.flowBotRequestsService.getFlowBotRequest(
      req.user,
      groupId,
      skip,
      take,
      where,
      orderBy,
    );
  }

  // Operate flow bot request
  @Patch(':groupId/flow-bot-requests/:flowBotRequestId')
  @Scopes('group-{groupId}:write-flow-bot-request')
  async updateFlowBotRequest(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number, // group id of bot
    @Param('flowBotRequestId', ParseIntPipe) flowBotRequestId: number,
    @Body() data: UpdateFlowBotRequestRequest,
  ): Promise<UpdateFlowBotRequestResponse> {
    return await this.flowBotRequestsService.updateFlowBotRequest(
      req.user.id,
      groupId,
      flowBotRequestId,
      data,
    );
  }

  // Cancel flow bot connection request
  @Patch(':groupId/flow-bot-requests/:flowBotRequestId/cancel')
  @Scopes('group-{groupId}:write-flow')
  async cancelFlowBotRequest(
    @Req() req: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('flowBotRequestId', ParseIntPipe) flowBotRequestId: number,
  ): Promise<GetFlowBotRequestResponse> {
    return await this.flowBotRequestsService.cancelFlowBotRequest(
      req.user.id,
      groupId,
      flowBotRequestId,
    );
  }

  @Get(':groupId/llm-model/upload-file-permision')
  @Scopes('group-{groupId}:read-llm-model-files')
  @UseInterceptors(ClassSerializerInterceptor)
  async getFilesWithPermision(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() request: UserRequest,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{ list: ModelFileEntity[]; count: number }> {
    const model = await this.llmModelService.findOneByGroupId(groupId);
    if (!model) throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);
    const group = await this.groupsService.getGroup(groupId, {});
    const rst = await this.llmModelService.findModelFilesWithPermision({
      skip,
      take,
      orderBy,
      where: { ...where, groupId },
      env: group.env,
      request,
    });
    const count = await this.llmModelService.getModelFilesCount({ ...where, groupId });
    const list = rst.map((item) => new ModelFileEntity(item));
    return { list, count };
  }

  @Post(':groupId/llm-model/one-tier-approvel-and-process-file')
  @Scopes('group-{groupId}:approve-or-process-llm-model-files')
  @UseInterceptors(ClassSerializerInterceptor)
  @ApiOperation({
    summary: 'One tier ApproveAndProcess file, When isApproved = false',
    tags: ['File Management'],
  })
  @ApiResponse({
    status: 201,
    description: 'OK',
    type: ModelFileEntity,
  })
  @AuditLog('approve-or-process-file')
  async oneTierApprovelAndprocessFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: ProcessFileDto,
  ): Promise<ModelFileEntity> {
    try {
      const group = await this.groupsService.getGroup(groupId, {});
      const doc = await this.llmModelService.oneTierApprovelAndprocessFile(group.env, data.docId);
      return new ModelFileEntity(doc);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Get(':groupId/llm-model/upload-file-permision/:fileId/info')
  @Scopes('group-{groupId}:read-llm-model-files')
  @UseInterceptors(ClassSerializerInterceptor)
  async getFileByFileIdWithPermision(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('fileId', ParseIntPipe) fileId: number,
    @Req() request: UserRequest,
  ): Promise<ModelFile> {
    const group = await this.groupsService.getGroup(groupId, {});
    const model = await this.llmModelService.findModelFileWithPermision(fileId, request, group.env);
    return model;
  }
  // list plan resource category
  @Get(':groupId/resources/category')
  @Scopes('group-{groupId}:read-resource-plan', 'system:read-resource-plan')
  async getGroupResourceCategory(@Param('groupId', ParseIntPipe) groupId: number) {
    return await this.groupsService.getGroupResourceCategory(groupId);
  }
  // list plan resource
  @Get(':groupId/resources')
  @Scopes('group-{groupId}:read-resource-plan', 'system:read-resource-plan')
  async getGroupResource(@Param('groupId', ParseIntPipe) groupId: number) {
    return await this.groupsService.getGroupResources(groupId);
  }

  // list plan
  @Get(':groupId/resources/plans')
  @Scopes('group-{groupId}:read-resource-plan', 'system:read-resource-plan')
  async getGroupResourcePlan(@Param('groupId', ParseIntPipe) groupId: number) {
    return await this.groupsService.getGroupResourcePlans(groupId);
  }
  // create plan changing request
  @Post(':groupId/resources/plans/request')
  @AuditLog('create-plan-request')
  @Scopes('group-{groupId}:write-resource-plan', 'system:write-resource-plan')
  async createPlanRequest(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: PlanRequestDto,
    @Req() request: UserRequest,
  ) {
    return await this.groupsService.createPlanRequest(
      groupId,
      request.user.id,
      data.subscribedPlanIds,
      data.customPlans,
    );
  }

  // list all group request list
  @Get(':groupId/resources/plans/requests')
  @Scopes('group-{groupId}:read-resource-plan', 'system:read-resource-plan')
  async getGroupPlanRequestList(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('where', WherePipe) where?: Record<string, unknown>,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const groupType = await this.groupsService.getGroupTypeByGroupId(groupId);

    const newWhere: Record<string, unknown> = {
      ...where,
      subscriberEntityType: groupType as ResourceSubsciberType,
      subscriberEntityId: groupId,
    };

    if (where?.['groupName']) {
      (newWhere['subscribedGroup']) = { name: where['groupName']};
      delete newWhere['groupName'];
    }
    const list = await this.plansService.getPlanRequests(newWhere, orderBy, true, skip, take);
    const count = await this.plansService.getPlanRequestCount(newWhere);
    return { list, count };
  }

  // get group request detail
  @Get(':groupId/resources/plans/request/:requestId')
  @Scopes('group-{groupId}:read-resource-plan', 'system:read-resource-plan')
  async getGroupPlanRequestDetail(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('requestId') requestId: number,
  ) {
    return await this.plansService.getPlanRequestDetail(requestId, true, groupId);
  }
  // get group latest updated time
  @Get(':groupId/usage-latest-date')
  @Scopes('group-{groupId}:read-resource-plan', 'system:read-resource-plan')
  async getGroupUsageUpdatedDarte() {
    return this.plansService.getUsageUpdatedDate();
  }

  @HttpCode(HttpStatus.OK)
  @Post(':groupId/llm-model/embeddings')
  @ExternalApi()
  @Scopes('group-{groupId}:read-playground', 'group-*:embeddings')
  @ApiOperation({ summary: 'Embeddings', description: '', tags: ['Embeddings'] })
  @ApiResponse({
    status: 200,
    description: 'OK',
    type: EmbeddingsResponseEntity,
  })
  async embeddingsGroupLLMModel(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() embeddingsDto: EmbeddingsDto,
    @Req() request: UserRequest,
  ): Promise<EmbeddingsResponse | void> {
    const group = await this.groupsService.getGroup(groupId, {});

    return await this.llmModelService.embeddings(group, request, embeddingsDto);
  }

  @Post(':groupId/llm-model/direct-process-file')
  @Scopes(
    'group-{groupId}:process-llm-model-files',
    'group-{groupId}:approve-or-process-llm-model-files',
  )
  @AuditLog('direct-process-file')
  async directProcessFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: ProcessFileDto,
  ): Promise<ModelFileEntity> {
    const group = await this.groupsService.getGroup(groupId, {});
    const doc = await this.llmModelService.processModelFile(group.env, data.docId);
    return new ModelFileEntity(doc);
  }
}
