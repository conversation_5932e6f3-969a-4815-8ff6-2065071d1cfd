import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  Logger,
  Res,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { CreateLlmModelDto } from './dto/create-llm-model.dto';
import { UpdatePublicLlmModelDto } from './dto/update-llm-model.dto';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { LLMModel, Group, LabelType, LabelEntityType, GroupType } from '.prisma/client';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { ApiKeysService } from '../api-keys/api-keys.service';
import {
  Environment,
  FileClassification,
  FileStatus,
  PrismaClient,
  SystemName,
  User,
  HasPromptInjection,
  ChatFileType,
  PiiFileStatus,
  ScanMalwareStatus,
  HasMalware,
  Feature,
  ChatFile,
  FileVerifyStatus as VerifyStatus,
  UserBookmarkEntityType,
  ChatSessionType,
} from '@prisma/client';
import { ModelFile, Prisma, ModelFilePermissionButton } from '@prisma/client';
import { LLMBackendService } from '../../providers/llm-backend/llm-backend.service';
import {
  CHAT_APPROACH,
  EMBEDDINGS_MODEL,
  EmbeddingsResponse,
  SOURCE,
  Usage,
  MODEL,
} from '../../providers/llm-backend/llm-backend.interface';
import { Expose } from '../../providers/prisma/prisma.interface';

import { PromotableService } from '../change-management/interfaces/promotable-service.interface';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { MailService } from 'src/providers/mail/mail.service';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosResponse } from 'axios';
import { UpdateModelFileStatusDto } from 'src/modules/embeddings/embeddings.dto';
import { GROUP_MEMBERSHIP_SCOPE_KEY } from 'src/providers/redis/redis.constants';
import { RedisService } from 'src/providers/redis/redis.service';
import { S3Service } from 'src/providers/s3/s3.service';
import { UserRequest } from '../auth/auth.interface';
import { ChatFilesService } from '../chat-files/chat-files.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import {
  ChannelType,
  ChatLlmModelDto,
  ChatRequester,
  File,
  FlowCallChatQuery,
  LlmModelBasicInfoResponse,
} from '../llm-models/dto/chat-llm-model.dto';
import { ActivateGroupDto } from '../groups/groups.dto';

import { ApiResourceService } from '../api-resource/api-resource.service';
import { FlowBotsService } from '../flow-bots/flow-bots.service';
import moment from 'moment';
import { GroupsService } from '../groups/groups.service';
import { UsersService } from '../users/users.service';
import { Response } from 'express';
import { Configuration } from 'src/config/configuration.interface';
import apm from 'elastic-apm-node';
import { ElasticSearchService } from 'src/providers/elasticsearch/elasticsearch.service';
import { EmbeddingsDto } from './dto/embeddings-llm-model.dto';
import { LlmModelParamsDto } from './dto/llm-model-params.dto';
import { FortiSanboxService } from 'src/providers/fortisandbox/fortisandbox.service';
import { ChatSessionsService } from '../chat-sessions/chat-sessions.service';
import { GetOverrideFeatureFlagsOrDefaultDTO } from '../feature-flags/feature-flags-model.dto';
import {
  SendGroupNotificationContentType,
  SendGroupNotificationDto,
} from '../group-notification/group-notification.dto';
import { GroupNotificationService } from '../group-notification/group-notification.service';
import { QueueService } from 'src/providers/queue/queue.service';
import { subYears, format } from 'date-fns';
import { LabelsService } from '../labels/labels.service';
import { PublicLLMModelWhereDto } from './dto/list-llm-model.dto';
import { HasPii } from './dto/upload-llm-model-file.dto';
import { Readable } from 'stream';

@Injectable()
export class LLMModelsService implements PromotableService {
  private logger = new Logger(LLMModelsService.name);
  private notNeedToLogRequesterChannel = [ChannelType.CHAT_WITH_DATA];
  private notNeedToLogRequestInfoToResponse = [ChannelType.CHAT_WITH_DATA];
  private notNeedToLogChatContentChannel = [ChannelType.OUTLOOK];
  private chatApproachFeatureMap = {
    [CHAT_APPROACH.CWD]: Feature.CHAT_WITH_DATA,
    [CHAT_APPROACH.CWF]: Feature.CHAT_WITH_FILE,
    [CHAT_APPROACH.RRR]: Feature.CHAT,
  };
  private textToImageModels = [MODEL.DALLE_3];
  constructor(
    private redis: RedisService,
    private prisma: PrismaService,
    private apiKey: ApiKeysService,
    private llmBackendService: LLMBackendService,
    private featureFlagService: FeatureFlagService,
    private mailService: MailService,
    private configService: ConfigService,
    @Inject(forwardRef(() => ChatFilesService))
    private chatFilesService: ChatFilesService,
    private s3Service: S3Service,
    private readonly usersService: UsersService,
    private readonly apiKeysService: ApiKeysService,
    private readonly apiResourceService: ApiResourceService,
    private readonly flowBotsService: FlowBotsService,
    private readonly elasticSearchService: ElasticSearchService,
    private fortiSanboxService: FortiSanboxService,
    private chatSessionsService: ChatSessionsService,
    private groupNotificationService: GroupNotificationService,
    private readonly sqsService: QueueService,
    private readonly labelsService: LabelsService,
    private readonly groupService: GroupsService,
  ) {}

  async createModel(createLlmModelDto: CreateLlmModelDto, tx: Prisma.TransactionClient) {
    return await tx.lLMModel.create({
      data: createLlmModelDto,
    });
  }

  async getModel(modelId: string, apiKey: string): Promise<LLMModel> {
    try {
      const group = await this.apiKey.getGroupFromApiKey(apiKey);
      const model = await this.prisma.lLMModel.findFirst({
        where: {
          modelId,
        },
      });
      if (!model) {
        throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);
      }
      if (model.groupId !== group.id) {
        throw new ApiException(ErrorCode.INVALID_LLM_MODEL_GROUP);
      }
      return model;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async findAll(
    groupId: number,
    params: {
      skip?: number;
      take?: number;
      cursor?: Prisma.LLMModelWhereUniqueInput;
      where?: Prisma.LLMModelWhereInput;
      orderBy?: Prisma.LLMModelOrderByWithRelationInput;
    },
    apiKey: string,
  ): Promise<LLMModel[]> {
    const { skip, take, cursor, where, orderBy } = params;
    try {
      const models = await this.prisma.lLMModel.findMany({
        skip,
        take,
        cursor,
        where: { ...where, group: { id: groupId } },
        orderBy,
      });
      return models;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async checkUserHasPermissionsOrIsPublicBot(
    req: UserRequest,
    llmModel: LLMModel,
    env: Environment,
  ) {
    const isPublicBot = llmModel.makeLiveToPublic && llmModel.active && env == 'PROD';
    if (isPublicBot || (await this.groupService.userHasAccessOrIsSudo(req, llmModel.groupId))) {
      return true;
    }
    throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
  }

  async findOneByGroupId(groupId: number): Promise<LLMModel> {
    try {
      const model = await this.prisma.lLMModel.findFirst({
        where: {
          groupId,
        },
        include: {
          // files: true,
          group: true,
        },
      });
      if (!model) {
        throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);
      }
      return model;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async findModelFiles(params: {
    skip?: number;
    take?: number;
    where?: Prisma.ModelFileWhereInput;
    orderBy?: Prisma.ModelFileOrderByWithRelationInput;
  }): Promise<ModelFile[]> {
    const { skip, take, where, orderBy } = params;
    // Validate and restructure tags for where clause if where.tags exist
    if (where.tags) where.tags = this.restructureWhereTags(where.tags);
    const modelFiles = await this.prisma.modelFile.findMany({
      skip,
      take,
      where,
      orderBy,
      include: {
        tags: {
          select: {
            id: true,
            Labels: { select: { name: true } },
          },
        },
      },
    });
    return modelFiles.map((modelFile) => {
      if (modelFile.tags) {
        // @ts-expect-error 2322 restructure tags for frontend
        modelFile.tags = modelFile.tags.map((t) => ({
          id: t.id,
          name: t.Labels.name,
        }));
      }
      return modelFile;
    });
  }

  async findModelFile(fileId: number): Promise<ModelFile> {
    return await this.prisma.modelFile.findUnique({
      where: {
        id: fileId,
      },
    });
  }

  async getModelFilesCount(where: Prisma.ModelFileWhereInput, includesDeleted?: boolean) {
    // Validate and restructure tags for where clause if where.tags exist
    if (where.tags) where.tags = this.restructureWhereTags(where.tags);
    return await this.prisma.modelFile.count({
      where: { ...where, ...(!includesDeleted ? { deletedAt: null } : {}) },
    });
  }

  async deleteModelFileByDocId(model: LLMModel, docId: string, request: UserRequest) {
    try {
      const file = await this.prisma.modelFile.findFirst({
        where: {
          docId,
        },
      });
      if (!file) throw new NotFoundException('File cannot be found in DB');

      if (file.status == 'PROCESSING') {
        if (file.indexingStartDate) {
          const currentTime = new Date();
          const twentyFourHoursAgo = new Date(currentTime.getTime() - 24 * 60 * 60 * 1000);
          if (file.indexingStartDate > twentyFourHoursAgo) {
            throw new ApiException(ErrorCode.MODEL_FILE_DELETE_PROCESSING);
          }
        }
      }

      const group = await this.getGroup(model.groupId);

      await this.llmBackendService.removeS3ModelFile(group.env, file.s3Path);

      if (file.fullScanReportPath && file.fullScanReportPath.trim() !== '') {
        await this.llmBackendService.removeS3PIIReportFile(group.env, file.fullScanReportPath);
      }

      if (file.status === 'COMPLETED') {
        await this.llmBackendService.removeModelFile(group.env, model.modelId, docId);
      }
      return await this.prisma.modelFile.update({
        where: {
          id: file.id,
        },
        data: {
          deletedAt: new Date(),
          deletedBy: request.user.id ?? 0,
        },
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getModelFileByDocId(file: ModelFile, model: LLMModel, @Res() res: Response) {
    try {
      if (!file) throw new NotFoundException('File cannot be found in DB');

      const group = await this.getGroup(model.groupId);
      const fileResponse = await this.llmBackendService.getS3ModelFile(group.env, file.s3Path);
      (fileResponse.Body as Readable).on('httpHeaders', (statusCode, headers) => {
        res.setHeader(
          'Content-Disposition',
          `attachment; filename*=UTF-8''${encodeURIComponent(file.filename)}`,
        );
        res.setHeader('Content-Type', `${file.filetype}`);
        res.setHeader('Content-Length', headers['content-length']);
      }).pipe(res);

    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
  async getModelFileInfoByDocId(docId: string) {
    try {
      const file = await this.prisma.modelFile.findFirst({
        where: {
          docId,
        },
      });
      return file;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  findOne(id: number) {
    return `This action returns a #${id} llmModel`;
  }

  /**
   * @description this function is for update the chat config the messageTemplateId param just will pass for the api call. when the promoteUpdate will pass undefined
   * @param modelId
   * @param tone
   * @param startupMessage
   * @param llmEngineId
   * @param userId
   * @param active
   * @param parameters
   * @param tx
   * @param showInTeams
   * @param messageTemplateId
   * @returns
   */
  async update(
    modelId: string,
    tone: string,
    startupMessage: string,
    llmEngineId: number,
    typeDefinition: string,
    showReference: boolean,
    userId: number,
    parameters: Prisma.NullableJsonNullValueInput | Prisma.InputJsonValue,
    tx: Prisma.TransactionClient,
    messageTemplateId?: number,
  ) {
    try {
      const data: Record<string, string | number | unknown> = {};
      const model = await tx.lLMModel.findFirst({
        where: {
          modelId,
        },
        include: { group: true, lastModifiedBy: true },
      });

      if (!model) {
        throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);
      }
      if (tone != null) {
        data['tone'] = tone;
      }
      if (startupMessage && startupMessage.trim().length > 0) {
        data['startupMessage'] = startupMessage;
      }
      if (typeDefinition && typeDefinition.trim().length > 0) {
        data['typeDefinition'] = typeDefinition;
      }
      if (showReference !== null && typeof showReference !== 'undefined') {
        data['showReference'] = showReference;
      }
      if (llmEngineId) {
        // data.llmEngineId = llmEngineId;
        const engine = await tx.llmEngine.findUnique({
          where: {
            id: llmEngineId,
          },
        });
        data['llmEngine'] = {
          connect: { id: llmEngineId },
        };
        data['modelEngine'] = engine.slug;
      }
      const updatedBackend = await this.llmBackendService.updateModelTonesOrStartupMessage(
        model.group?.env,
        modelId,
        tone,
        startupMessage,
      );
      if (!updatedBackend) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_BACKEND_MODEL);
      }

      if (messageTemplateId) {
        await tx.messageTemplate.update({
          where: { id: messageTemplateId },
          data: { used: { increment: 1 } },
        });
      }

      let parametersUpdate: any = parameters ?? null;
      if ((model.parameters as any)?.dataSource) {
        parametersUpdate = parameters || {};
        parametersUpdate.dataSource = (model.parameters as any).dataSource;
      }
      const updated = await tx.lLMModel.update({
        where: {
          modelId,
        },
        include: {
          group: true,
        },
        data: {
          ...data,
          parameters: parametersUpdate,
          lastModifiedBy: { connect: { id: userId } },
        },
      });
      if (!updated) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }
      await this.redis.batchClearCache(
        GROUP_MEMBERSHIP_SCOPE_KEY.replace('{USER_ID}', '*').replace(
          '{GROUP_ID}',
          model.groupId.toString(),
        ),
      );
      return updated;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async updateModelFile(
    model: LLMModel,
    userId: number,
    file: Express.Multer.File,
    fileUuid: string,
    uploadLocation: string,
    fileSize: number,
    fileClassification: FileClassification,
    autoIndex?: boolean,
  ) {
    try {
      // this.logger.log({
      //   filename: file.originalname,
      //   filetype: file.mimetype,
      //   fileExt: file.originalname.split('.').pop().toLowerCase(),
      //   docId: fileUuid,
      //   userId,
      //   uploadLocation,
      //   model: {
      //     connect: {
      //       id: model.id,
      //     },
      //   },
      //   group: {
      //     connect: {
      //       id: model.groupId,
      //     },
      //   },
      // });
      const byPassScanMalware = await this.featureFlagService.getDefaultFeatureFlagFromDb(
        FeatureFlagKey.BYPASS_SCAN_MALWARE,
      );

      const byPassScanPII = await this.featureFlagService.getDefaultFeatureFlagFromDb(
        FeatureFlagKey.BYPASS_SCAN_PII,
      );

      const updatedDoc = await this.prisma.modelFile.create({
        data: {
          filename: file.originalname,
          filetype: file.mimetype,
          fileExt: file.originalname.split('.').pop().toLowerCase(),
          docId: fileUuid,
          s3Path: uploadLocation,
          fileSize,
          fileClassification,
          fullScanReportPath: undefined,
          fullScanReportCreatedAt: undefined,
          fullScanReportUpdatedAt: undefined,
          errCode: undefined,
          hasPii: byPassScanPII?.isEnabled ? null : 'PENDING',
          detectedPii: undefined,
          piiFileStatus: undefined,
          scanMalwareStatus: byPassScanMalware?.isEnabled ? null : ScanMalwareStatus.PENDING,
          autoIndex: autoIndex ?? true,
          model: {
            connect: {
              id: model.id,
            },
          },
          group: {
            connect: {
              id: model.groupId,
            },
          },
          uploader: {
            connect: {
              id: userId,
            },
          },
        },
      });
      return updatedDoc;
    } catch (err) {
      this.logger.log(err);
      throw err;
    }
  }

  remove(id: number) {
    return `This action removes a #${id} llmModel`;
  }

  async isRequireSecondaryFileApproval(groupId: number) {
    const approveFeatureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.ENABLE_SECONDARY_APPROVAL,
    );
    return approveFeatureFlag?.isEnabled;
  }
  async getBotReviewerCount(groupId: number) {
    const botReviewerCount = await this.prisma.membership.count({
      where: {
        groupId: groupId,
        user: {
          userRole: {
            systemName: SystemName.BOT_REVIEWER,
          },
        },
      },
    });
    const norminationCount = await this.prisma.botReviewNomination.count({
      where: {
        membership: { groupId: groupId },
        startDate: { lte: new Date() },
        endDate: { gte: new Date() },
      },
    });

    return botReviewerCount + norminationCount;
  }

  async approveOrProcessModelFile(group: Group, docId: string, isFinalApproval: boolean) {
    const data: Record<string, string | number | boolean> = {
      isApproved: true,
    };
    const isRequireSecondaryFileApproval = await this.isRequireSecondaryFileApproval(group.id);
    if (isFinalApproval || !isRequireSecondaryFileApproval) {
      data['status'] = FileStatus.APPROVED;
    }
    const modelFile = await this.prisma.modelFile.update({
      include: {
        uploader: true,
      },
      where: {
        docId,
      },
      data: data,
    });

    if (!isFinalApproval && isRequireSecondaryFileApproval) {
      try {
        // send emails
        const frontendUrl = this.configService.get<string>('frontendUrl');
        const templateData = {
          env: group.env,
          groupName: group.name,
          groupId: group.id,
          filename: decodeURI(modelFile.filename),
          fileId: modelFile.id,
          uploaderName: modelFile.uploader.name,
          uploadedAt: modelFile.createdAt,
          link: `${frontendUrl}/v2/groups/${group.id}/files`,
        };
        const sendGroupNotificationDto = {
          sourceId: `file-process-approval-request-${modelFile.docId}-${modelFile.id}-${group.id}`,
          contentType: SendGroupNotificationContentType.Template,
          notificationKey: 'file-process-approval-request',
          data: templateData,
          templateName: 'llm-model/approved-file',
        } as SendGroupNotificationDto;
        await this.groupNotificationService.sendGroupNotification(
          group.id,
          sendGroupNotificationDto,
        );
      } catch (err) {
        this.logger.error(err, 'sending approval email ');
      }
      return modelFile;
    }
    if (modelFile.autoIndex) {
      return this.processModelFile(group.env, docId);
    } else {
      return modelFile;
    }
  }

  async processModelFile(env: string, docId: string) {
    const modelFile = await this.prisma.modelFile.findUnique({
      where: {
        docId,
      },
      include: {
        model: {
          include: {
            group: true,
          },
        },
      },
    });
    if (!modelFile) {
      throw new NotFoundException('Unable to find the model file in the database.');
    }

    if (modelFile.status != FileStatus.APPROVED) {
      throw new ApiException(ErrorCode.MODEL_FILE_NOT_APPROVED);
    }

    const newModelFile = await this.llmBackendService.processModelFile(
      modelFile.model.group.env,
      modelFile,
    );
    return newModelFile;
  }

  async updateModelFileStatus(
    fileUuid: string,
    data: UpdateModelFileStatusDto,
    request: UserRequest,
  ) {
    const { status, usage } = data;
    try {
      const fileRecord = await this.prisma.modelFile.findFirst({
        where: {
          docId: fileUuid,
        },
      });

      if (fileRecord == null) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }
      let updateStatus: FileStatus = 'UPLOADED';
      if (status) {
        if (status == 'verify_success') updateStatus = 'VERIFY_SUCCESS';
        else if (status === 'processing') updateStatus = 'PROCESSING';
        else if (status === 'verify_failed') updateStatus = 'VERIFY_FAILED';
        else if (status === 'completed') updateStatus = 'COMPLETED';
      }

      if (
        ((updateStatus === 'VERIFY_SUCCESS' || updateStatus === 'VERIFY_FAILED') &&
          fileRecord.status !== 'UPLOADED') ||
        (updateStatus === 'COMPLETED' && fileRecord.status !== 'PROCESSING')
      ) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }

      const updated = await this.prisma.modelFile.update({
        where: {
          docId: fileUuid,
        },
        data: {
          status: updateStatus,
          errorMsg: data?.error || null,
        },
      });

      if (!updated) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }

      if (status == 'completed') {
        const group = await this.prisma.group.findFirst({ where: { id: fileRecord.groupId } });
        const embeddingsDto: EmbeddingsDto = {
          overrides: { model: EMBEDDINGS_MODEL.TEXT_EMBEDDING_ADA_002 },
          texts: [],
        };
        const prefilledLog = await this.llmBackendService.generateEmbeddingsPrefilledLog(
          group,
          request,
          embeddingsDto,
        );

        await this.logChatResponse(prefilledLog, usage, '');
      }

      return updated;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async updateEmbeddingsVerificationStatus(fileUuid: string, dto: UpdateModelFileStatusDto) {
    try {
      const fileRecord = await this.prisma.modelFile.findFirst({
        where: {
          docId: fileUuid,
        },
      });

      if (fileRecord == null) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }
      let updateStatus: FileStatus = 'UPLOADED';

      const data: Record<string, string | number | unknown> = {};

      if (dto.status) {
        if (dto.status == 'verify_success') updateStatus = 'VERIFY_SUCCESS';
        else if (dto.status === 'processing') updateStatus = 'PROCESSING';
        else if (dto.status === 'verify_failed') updateStatus = 'VERIFY_FAILED';
        else if (dto.status === 'completed') updateStatus = 'COMPLETED';
      }

      if (
        ((updateStatus === 'VERIFY_SUCCESS' || updateStatus === 'VERIFY_FAILED') &&
          fileRecord.status !== 'UPLOADED') ||
        (updateStatus === 'COMPLETED' && fileRecord.status !== 'PROCESSING')
      ) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }

      data['status'] = updateStatus;
      data['errorMsg'] = dto.error || null;
      data['hasPii'] = dto.hasPii ? dto.hasPii.toLocaleUpperCase() : null;
      data['errCode'] = fileRecord.errCode
        ? `${fileRecord.errCode}, ${dto.errCode}`
        : dto.errCode || null;
      data['detectedPii'] = dto.detectedPii || null;

      const updated = await this.prisma.modelFile.update({
        where: {
          docId: fileUuid,
        },
        data: {
          ...data,
        },
      });

      if (!updated) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }

      return updated;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async getGroup(id: number): Promise<Expose<Group>> {
    const group = await this.prisma.group.findUnique({
      where: { id },
    } as any);
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    return this.prisma.expose<Group>(group);
  }

  async getLLMModelByGroupId(groupId: number) {
    return await this.prisma.lLMModel.findFirst({ where: { groupId } });
  }

  async generateEntityDataForSnapshot(groupId: number, entityId: string): Promise<object> {
    const lLMModel = await this.prisma.lLMModel.findFirst({
      where: {
        groupId,
        id: parseInt(entityId),
      },
    });
    if (!lLMModel) throw new ApiException(ErrorCode.LLM_MODEL_NOT_FOUND);
    return lLMModel;
  }

  async deleteEntityDataForSnapshot(
    groupId: number,
    entityId: string,
    entityData: any,
  ): Promise<void> {
    return;
  }

  async promoteCreate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    entityData: LLMModel,
    operatorId: number,
  ): Promise<string> {
    // since PROD llmModel is created by default without promotion, call promoteUpdate here
    const llmModel = await tx.lLMModel.findFirst({
      select: { id: true },
      where: { groupId: targetGroup.id },
    });
    if (!llmModel) throw new ApiException(ErrorCode.LLM_MODEL_NOT_FOUND);
    await this.promoteUpdate(tx, targetGroup, llmModel.id.toString(), entityData, operatorId);
    return llmModel.id.toString();
  }

  async promoteUpdate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    entityData: LLMModel,
    operatorId: number,
  ): Promise<void> {
    const srcLlmEngine = await tx.llmEngine.findUniqueOrThrow({
      where: { id: entityData.llmEngineId },
    });
    // find the engine for destination env
    const dstEnvLlmEngine = await tx.llmEngine.findUniqueOrThrow({
      where: {
        slug: srcLlmEngine.slug,
      },
    });
    const dstLlmModel = await tx.lLMModel.findUniqueOrThrow({
      where: { id: parseInt(targetEntityId) },
    });

    await tx.lLMModel.update({
      data: {
        lastPromotedAt: new Date(),
      },
      where: {
        id: parseInt(targetEntityId),
      },
    });

    // plug into original update function
    await this.update(
      dstLlmModel.modelId,
      entityData.tone,
      entityData.startupMessage,
      dstEnvLlmEngine.id,
      entityData.typeDefinition,
      entityData.showReference,
      operatorId,
      entityData.parameters,
      tx,
    );
  }

  async promoteDelete(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    operatorId: number,
  ): Promise<void> {
    throw new ApiException(ErrorCode.DATA_PROMOTION_DELETE_NOT_SUPPORTED);
  }

  async checkPromotedEntityValid(targetEntityId: string) {
    return this.prisma.lLMModel.findUnique({ where: { id: parseInt(targetEntityId) } });
  }
  /**
   * @description this function will checking user upload file limit with day(UTC+8) and return boolean
   * . it will check with the FeatureFlagKey CONFIG_MAX_UPLOADED_FILES_PER_USER_PER_DAY
   * if the CONFIG_MAX_UPLOADED_FILES_PER_USER_PER_DAY is disabled then it will check with the default value 10
   *  if Exceed limit will throw error
   * @param groupId
   * @param userId
   *
   */
  async checkExceedPreUserPreDayUploadLimit(
    groupId: number,
    userId: number,
    addFileCount?: number,
  ) {
    let userUploadFileCounts = await this.getModelFilesCount(
      {
        groupId: groupId,
        uploaderId: userId,
        createdAt: {
          gte: moment().startOf('day').utcOffset(8).toDate(),
          lte: moment().endOf('day').utcOffset(8).toDate(),
        },
      },
      true,
    );
    const featureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.CONFIG_MAX_UPLOADED_FILES_PER_USER_PER_DAY,
    );
    let featureLimit = 10;
    if (featureFlag?.isEnabled) {
      featureLimit = featureFlag.metaData['value'] as number;
    }
    if (addFileCount) {
      userUploadFileCounts += addFileCount;
    }
    if (featureLimit <= userUploadFileCounts) {
      throw new ApiException(
        ErrorCode.FILE_UPLOAD_PRE_DAY_LIMIT_EXCEEDED.replaceAll('{limit}', featureLimit.toString()),
      );
    }
  }

  async findS3FileKeyByDocIds(groupId: number, docIds: string[]): Promise<ModelFile[]> {
    return await this.prisma.modelFile.findMany({
      where: {
        groupId,
        docId: {
          in: docIds,
        },
      },
    });
  }

  async collectS3Paths(groupId: number, userId: number, files: File[]): Promise<File[]> {
    const vectorStoreTypeFiles: File[] = files.filter(
      (file) => file.data_source === SOURCE.VECTOR_STORE,
    );

    // collect ModelFiles for ModelFile s3 key list
    let modelFiles: ModelFile[] = [];
    if (vectorStoreTypeFiles.length) {
      const distinctModelFileDocIds: string[] = [
        ...new Set(vectorStoreTypeFiles.map((file) => file.file_id)),
      ];
      modelFiles = await this.findS3FileKeyByDocIds(groupId, distinctModelFileDocIds);
    }
    const uploadTypeFiles: string[] = files
      .filter((file) => file.data_source === SOURCE.UPLOAD)
      .map((file) => file.file_id);
    if (
      (!userId && uploadTypeFiles.length > 0) ||
      files.length !== modelFiles.length + uploadTypeFiles.length
    ) {
      throw new ApiException(ErrorCode.FIlE_NOT_FOUND);
    }

    for (const file of files) {
      if (file.data_source === SOURCE.VECTOR_STORE) {
        const modelFile = modelFiles.find((i) => i.docId === file.file_id);
        file['s3_key'] = modelFile.s3Path;
      } else if (file.data_source === SOURCE.UPLOAD) {
        file['s3_key'] = this.chatFilesService.getS3FileKey(
          groupId,
          userId,
          file.file_id,
          ChatFileType.QUESTION,
        );
      }
      delete file.file_id;
    }

    return files;
  }
  async activateGroup(id: number, data: ActivateGroupDto) {
    await this.prisma.lLMModel.update({
      where: { groupId: id },
      data,
    });
    await this.redis.batchClearCache(
      GROUP_MEMBERSHIP_SCOPE_KEY.replace('{USER_ID}', '*').replace('{GROUP_ID}', id.toString()),
    );
    return { ...data, id };
  }

  // for llmguard callback
  async updateFullScanForLlmguard(
    fid: string,
    s3SavePath: string,
    hasPromptInjection: boolean,
    hasPII: boolean,
    detectedPii: string,
    type: string,
    piiFileStatus: string,
  ) {
    try {
      let modelFile;
      if (type == 'id') {
        const id = parseInt(fid);
        modelFile = await this.prisma.modelFile.findFirst({
          where: {
            id,
          },
        });
      } else {
        const s3Path = fid;
        modelFile = await this.prisma.modelFile.findFirst({
          where: {
            s3Path,
          },
        });
      }

      if (!modelFile) throw new NotFoundException('ModelFile cannot be found in DB');

      const data: Record<string, string | number | unknown> = {};

      data['fullScanReportPath'] = s3SavePath;
      data['piiFileStatus'] = piiFileStatus;
      data['hasPromptInjection'] = hasPromptInjection
        ? HasPromptInjection.YES
        : HasPromptInjection.NO;
      data['hasPii'] = hasPII ? HasPii.YES : HasPii.NO;
      data['detectedPii'] = hasPII ? detectedPii : '';

      if (
        modelFile.fullScanReportCreatedAt == null ||
        modelFile.fullScanReportCreatedAt == undefined
      ) {
        data['fullScanReportCreatedAt'] = new Date();
        data['fullScanReportUpdatedAt'] = new Date();
      } else {
        data['fullScanReportUpdatedAt'] = new Date();
      }

      const updatedModelFile = await this.prisma.modelFile.update({
        where: {
          id: modelFile.id,
        },
        data: {
          ...data,
        },
      });
      return { status: 200, msg: 'suc updatedModelFile' };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async securityScan(id: number) {
    try {
      const fileRecord = await this.prisma.modelFile.findFirst({
        where: {
          id,
        },
      });
      if (!fileRecord) throw new NotFoundException('ModelFile cannot be found in DB');
      this.modelFileMalwareScan(fileRecord).catch((error: Error) =>
        this.logger.error(`modelFileMalwareScan ${error.message}`, error.stack),
      );
      return { status: 200, msg: 'succ to rescan' };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  //download by s3
  async downloadfullScanPiiFile(id: number, isChatFile?: boolean) {
    try {
      let file;
      if (isChatFile) {
        file = await this.prisma.chatFile.findFirst({
          where: {
            AND: [
              { id: id },
              { NOT: { fullScanReportPath: null } },
              { NOT: { fullScanReportPath: '' } },
            ],
          },
          include: { group: true },
        });
      } else {
        file = await this.prisma.modelFile.findFirst({
          where: {
            AND: [
              { id: id },
              { NOT: { fullScanReportPath: null } },
              { NOT: { fullScanReportPath: '' } },
            ],
          },
          include: { group: true },
        });
      }

      if (!file)
        throw new NotFoundException(
          `${
            isChatFile ? 'ChatFile' : 'ModelFile'
          } cannot be found in DB or fullScanReportPath is null`,
        );

      return this.s3Service.getObjectUnsafe(
        this.configService.get<string>(`s3.piiReportBuckets.${file.group.env}`),
        file.fullScanReportPath,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async findModelFilesWithPermision(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.ModelFileWhereUniqueInput;
    where?: Prisma.ModelFileWhereInput;
    orderBy?: Prisma.ModelFileOrderByWithRelationInput;
    env?: string;
    request: UserRequest;
  }): Promise<ModelFile[]> {
    const { skip, take, cursor, where, orderBy, env, request } = params;

    // Validate and restructure tags for where clause if where.tags exist
    if (where.tags) where.tags = this.restructureWhereTags(where.tags);

    const oneYearAgo = subYears(new Date(), 1);
    const oneYearAgoStr = format(oneYearAgo, 'yyyyMMdd');

    const fileList = await this.prisma.modelFile.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
      include: {
        tags: {
          select: {
            id: true,
            Labels: { select: { name: true } },
          },
          where: {
            LabelEntityType: LabelEntityType.MODEL_FILE,
            Labels: { labelType: LabelType.TAG },
          },
        },
        uploader: {
          select: {
            name: true,
            emails: { select: { email: true } },
            prefersEmail: { select: { email: true } },
          },
        },
        historicalFileSecurityReports: true,
        historicalFileMalwareScan: {
          where: {
            scanMalwareVersion: {
              gte: oneYearAgoStr,
            },
          },
        },
      },
    });

    const matchedPermissions = await this.getDbDefaultPermision(request);

    const buttonCoditionList = await this.prisma.modelFilePermissionButton.findMany({
      where: {
        permissionKey: { in: matchedPermissions },
      },
    });

    const isRequireSecondaryApproval = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.ENABLE_SECONDARY_APPROVAL,
    );

    const byPassScanMalware = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.BYPASS_SCAN_MALWARE,
    );

    const overrideFeatureFlagMap = await this.featureFlagService.getAllGroupOverrideFeatureFlag(
      FeatureFlagKey.BOT_MALWARE_RATING_WHITELIST,
    );

    const result = [];
    for (const modelFile of fileList) {
      const overrideFeatureFlag =
        overrideFeatureFlagMap.get(String(modelFile.groupId)) ??
        overrideFeatureFlagMap.get('default');

      if (modelFile.tags) {
        // @ts-expect-error 2322 restructure tags for frontend
        modelFile.tags = modelFile.tags.map((t) => ({
          id: t.id,
          name: t.Labels.name,
        }));
      }

      if (modelFile.historicalFileMalwareScan) {
        modelFile.historicalFileMalwareScan = modelFile.historicalFileMalwareScan.map(
          (historicalFileMalwareScan) => {
            const rating = historicalFileMalwareScan.malwareRating;
            const ratingWhitelist: string[] = overrideFeatureFlag.isEnabled
              ? (overrideFeatureFlag.metaData['values'] as string[]) ?? ['CLEAN']
              : ['CLEAN'];

            const isClean = rating ? ratingWhitelist.includes(rating.toUpperCase()) : false;
            return {
              ...historicalFileMalwareScan,
              isClean,
            };
          },
        );
      }

      const entity = await this.getModelFileEntity(
        modelFile,
        buttonCoditionList,
        isRequireSecondaryApproval?.isEnabled,
        byPassScanMalware?.isEnabled,
        overrideFeatureFlag,
      );

      result.push(entity);
    }
    return result;
  }

  async getDbDefaultPermision(request: UserRequest): Promise<string[]> {
    const scopesList = request.user.scopes;
    //handle user has what permision
    const permissionKeyMap = await this.prisma.modelFilePermissionButton
      .findMany({
        select: {
          permissionKey: true,
        },
        distinct: ['permissionKey'],
      })
      .then((results) => {
        const map = new Map<string, string>();
        results.forEach((result) => {
          let updatedPermissionKey = result.permissionKey;
          if (updatedPermissionKey.includes('{groupId}')) {
            updatedPermissionKey = updatedPermissionKey.replace(
              '{groupId}',
              String(request.user.groupId),
            );
          } else if (updatedPermissionKey.includes('{userId}')) {
            updatedPermissionKey = updatedPermissionKey.replace(
              '{userId}',
              String(request.user.id),
            );
          }
          map.set(updatedPermissionKey, result.permissionKey);
        });
        return map;
      });

    const matchedPermissions = [];
    permissionKeyMap.forEach((value, key) => {
      if (scopesList.includes(key)) {
        matchedPermissions.push(value);
      }
    });
    return matchedPermissions;
  }

  getButtonList(
    buttonConditionList: ModelFilePermissionButton[],
    modelFile: ModelFile,
    isRequireSecondaryApproval: boolean,
    isbyPassScanMalware: boolean,
    overrideFeatureFlag?: GetOverrideFeatureFlagsOrDefaultDTO,
  ): string[] {
    let buttonList = [];

    for (const condition of buttonConditionList) {
      if (condition) {
        if (condition.isApproved !== null && condition.isApproved !== modelFile?.isApproved) {
          continue;
        }

        if (condition.status && condition.status !== modelFile?.status) {
          continue;
        }

        if (
          isRequireSecondaryApproval !== null &&
          condition.isRequireSecondaryApproval !== null &&
          condition.isRequireSecondaryApproval !== isRequireSecondaryApproval
        ) {
          continue;
        }

        if (
          condition.fileClassification &&
          condition.fileClassification !== modelFile?.fileClassification
        ) {
          continue;
        }

        if (!isbyPassScanMalware) {
          if (this.malwareCondition(condition, modelFile, overrideFeatureFlag)) {
            continue;
          }
        }

        buttonList.push(...condition.buttonList);
      }
    }

    if (buttonList.length > 0) {
      buttonList = [...new Set(buttonList)];
    }

    return buttonList;
  }

  async oneTierApprovelAndprocessFile(env: string, docId: string) {
    const isRequireSecondaryApproval = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.ENABLE_SECONDARY_APPROVAL,
    );

    const modelFile = await this.prisma.modelFile.findUnique({
      where: {
        docId,
      },
      include: {
        model: {
          include: {
            group: true,
          },
        },
      },
    });
    if (!modelFile) {
      throw new NotFoundException('Unable to find the model file in the database.');
    }

    let approvelFlag = true;
    if (modelFile.fileClassification !== FileClassification.PUBLIC_DOMAIN) {
      approvelFlag = !isRequireSecondaryApproval?.isEnabled ?? false;
    }
    let newModelFile: any = modelFile;
    if (approvelFlag) {
      await this.prisma.modelFile.update({
        include: {
          uploader: true,
        },
        where: {
          id: modelFile.id,
        },
        data: {
          isApproved: true,
          status: FileStatus.APPROVED,
        },
      });
      modelFile.isApproved = true;
    }

    if (!modelFile.isApproved) {
      throw new ApiException(ErrorCode.MODEL_FILE_NOT_APPROVED);
    }

    if (modelFile.autoIndex) {
      newModelFile = await this.llmBackendService.processModelFile(
        modelFile.model.group.env,
        modelFile,
      );
    }
    return newModelFile;
  }

  async findModelFileWithPermision(
    fileId: number,
    request: UserRequest,
    env: string,
  ): Promise<ModelFile> {
    const modelFile = await this.prisma.modelFile.findUnique({
      where: { id: fileId },
      include: {
        uploader: {
          select: {
            name: true,
            emails: { select: { email: true } },
            prefersEmail: { select: { email: true } },
          },
        },
        historicalFileSecurityReports: true,
        historicalFileMalwareScan: true,
      },
    });

    if (!modelFile) {
      throw new ApiException(ErrorCode.FIlE_NOT_FOUND);
    }

    const matchedPermissions = await this.getDbDefaultPermision(request);

    const buttonCoditionList = await this.prisma.modelFilePermissionButton.findMany({
      where: {
        permissionKey: { in: matchedPermissions },
      },
    });

    const isRequireSecondaryApproval = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.ENABLE_SECONDARY_APPROVAL,
    );

    const byPassScanMalware = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.BYPASS_SCAN_MALWARE,
    );

    const overrideFeatureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      modelFile.groupId,
      FeatureFlagKey.BOT_MALWARE_RATING_WHITELIST,
    );

    if (modelFile.historicalFileMalwareScan) {
      modelFile.historicalFileMalwareScan = modelFile.historicalFileMalwareScan.map(
        (historicalFileMalwareScan) => {
          const rating = historicalFileMalwareScan.malwareRating;
          const ratingWhitelist: string[] = overrideFeatureFlag.isEnabled
            ? (overrideFeatureFlag.metaData['values'] as string[]) ?? ['CLEAN']
            : ['CLEAN'];

          const isClean = rating ? ratingWhitelist.includes(rating.toUpperCase()) : false;
          return {
            ...historicalFileMalwareScan,
            isClean,
          };
        },
      );
    }

    return await this.getModelFileEntity(
      modelFile,
      buttonCoditionList,
      isRequireSecondaryApproval?.isEnabled,
      byPassScanMalware?.isEnabled,
      overrideFeatureFlag,
    );
  }

  async chatWithLLM(
    group: Group,
    request: UserRequest,
    chatRequest: ChatLlmModelDto,
    res: Response,
    flowQuery?: FlowCallChatQuery,
  ) {
    const isStream: boolean = chatRequest.overrides?.stream;
    const chatReq = await this.apiResourceService.appendApiResourceAsOverridesDataByGroupId(
      group.id,
      chatRequest,
    );
    if (flowQuery?.flowId) {
      await this.flowBotsService.validateFlowHasBotAccess(group.id, Number(flowQuery.flowId));
    }
    const prefilledLog = await this.generatePrefilledChatLog(group, request, chatReq, flowQuery);
    const llmRes = await this.llmBackendService.chatWithModel(
      group.env,
      chatReq,
      res,
      prefilledLog,
      isStream,
    );
    return llmRes;
  }

  async generatePrefilledChatLog(
    group: Group,
    request: UserRequest,
    chatLlmModelDto: ChatLlmModelDto,
    flowQuery?: FlowCallChatQuery,
  ) {
    let requester: ChatRequester = {
      requesterId: '',
      requesterName: '',
    };
    let feature = this.chatApproachFeatureMap[chatLlmModelDto.approach];
    if (this.textToImageModels.includes(chatLlmModelDto.overrides?.model)) {
      feature = Feature.TEXT_TO_IMAGE;
    }
    const channel = chatLlmModelDto.channel
      ? chatLlmModelDto.channel
      : request.headers['x-api-key'] != null
        ? ChannelType.API_KEY
        : ChannelType.PLAYGROUND;

    if (!this.notNeedToLogRequesterChannel.includes(channel)) {
      if (
        channel === ChannelType.API_KEY ||
        (channel === ChannelType.TEAMS && request.headers['x-api-key'] != null) ||
        (channel === ChannelType.OUTLOOK && request.headers['x-api-key'] != null)
      ) {
        if (request.headers['x-api-key'] && request.headers['x-api-key'].toString().length > 0) {
          try {
            const apiKey = await this.apiKeysService.getApiKeyFromKey(
              request.headers['x-api-key'].toString(),
            );
            requester = {
              requesterId: apiKey.id.toString(),
              requesterName: apiKey.name,
            };
          } catch (error) {
            if (error instanceof ApiException) {
              // Only not throw an error if the error is equal to API_KEY_NOT_FOUND (404-0-5).
              // If there is another error, we need to throw that specific error instead.
              this.logger.log(`API key not found: ${request.headers['x-api-key']}`);
            } else {
              throw error;
            }
          }
        }
      } else if (channel === ChannelType.AUTO_TEST) {
        requester.requesterId = request.user.id + '';
        requester.requesterName = 'AUTO_TEST';
      } else {
        // TODO need add new api for this
        const user = await this.usersService.getUser(request.user.id);
        const email = await this.prisma.email.findFirst({ where: { userId: request.user.id } });
        requester = {
          requesterId: user?.id?.toString(),
          requesterName: user?.name,
          requesterStaffId: user?.staffId,
          requesterEmail: email?.email,
        };
      }
    }
    const prefilledLog = {
      date: moment(),
      botId: group.id,
      botEnv: group.env,
      botName: group.name,
      channel,
      ...requester,
      engine: chatLlmModelDto.overrides?.model,
      engineConfig: chatLlmModelDto.overrides,
      query: String(chatLlmModelDto.history[chatLlmModelDto.history.length - 1].content).trim(),
      chatSessionName: chatLlmModelDto?.chatSession?.name,
      isChatSessionDefault: chatLlmModelDto?.chatSession?.isDefault,
      chatSessionId: chatLlmModelDto?.chatSession?.id,
      feature,
      ...(channel === ChannelType.TEAMS
        ? chatLlmModelDto.callingAttributes
          ? { callingAttributes: chatLlmModelDto.callingAttributes }
          : {}
        : {}),
    } as any;
    if (flowQuery && flowQuery?.flowId) {
      prefilledLog.flowId = Number(flowQuery.flowId);
    }
    return prefilledLog;
  }

  logChatResponse(prefilledLog: any, usage: Usage, answer: string) {
    if (this.notNeedToLogRequestInfoToResponse.includes(prefilledLog.channel)) {
      this.logger.log(
        `the channel is ${prefilledLog.channel} not need to log the info in opensearch`,
      );
      return;
    }
    if (this.notNeedToLogChatContentChannel.includes(prefilledLog.channel)) {
      prefilledLog.query = '**********';
      answer = '**********';
    }
    prefilledLog.durationInMS = moment().diff(prefilledLog.date, 'milliseconds');
    if (prefilledLog?.engineConfig?.api_resource) {
      delete prefilledLog.engineConfig.api_resource;
    }
    prefilledLog.usage = usage;
    prefilledLog.answer = answer;
    prefilledLog.traceId = apm?.currentTraceIds?.['trace.id'];
    this.logger.debug(prefilledLog);
    const trackingConfig = this.configService.get<Configuration['tracking']>('tracking');
    this.elasticSearchService.logChatRecord(
      `${trackingConfig.index}-${moment(prefilledLog.date).format('YYYY-MM-DD')}`,
      prefilledLog,
    );
  }

  insertUsageLog(usageLog: any) {
    if (this.notNeedToLogRequestInfoToResponse.includes(usageLog.channel)) {
      this.logger.log(`the channel is ${usageLog.channel} not need to log the info in opensearch`);
      return;
    }
    if (usageLog?.engineConfig?.api_resource) {
      delete usageLog.engineConfig.api_resource;
    }
    this.logger.debug(usageLog);
    const trackingConfig = this.configService.get<Configuration['tracking']>('tracking');
    this.elasticSearchService.logChatRecord(
      `${trackingConfig.index}-${moment(usageLog.date).format('YYYY-MM-DD')}`,
      usageLog,
    );
  }

  async findBasicInfoByGroupId(groupId: number): Promise<LlmModelBasicInfoResponse> {
    try {
      const model = await this.prisma.lLMModel.findFirst({
        where: {
          groupId,
        },
        include: {
          llmEngine: true,
          group: true,
        },
      });
      if (!model) {
        this.logger.error('LLM Model not found.');
        throw new ApiException(ErrorCode.LLM_MODEL_NOT_FOUND);
      }

      return {
        id: model.id,
        name: model.llmEngine.name,
        modelId: model.modelId,
        modelEngine: model.llmEngine.slug,
        modelEngineIsActive: model.llmEngine.isActive,
        llmEngineId: model.llmEngineId,
        startupMessage: model.startupMessage,
        parameters: model.parameters as LlmModelParamsDto,
        showRefInTeams: model.showRefInTeams,
        profilePictureUrl: model.group.profilePictureUrl,
        makeLiveToPublic: model.makeLiveToPublic,
        canShareChat: model.canShareChat,
      } as LlmModelBasicInfoResponse;
    } catch (err) {
      this.logger.error(
        err,
        '[LLMModelsService][findBasicInfoByGroupId] LLM Model basic info not found',
      );
      throw err;
    }
  }

  async embeddings(
    group: Group,
    request: UserRequest,
    embeddingsDto: EmbeddingsDto,
  ): Promise<EmbeddingsResponse> {
    return await this.llmBackendService.embeddings(group, request, embeddingsDto);
  }

  async updateEmbeddingsVerificationStatusWithOutLLmguard(
    fileUuid: string,
    data: { status: string; errCode: string; error: string },
  ) {
    try {
      const fileRecord = await this.prisma.modelFile.findFirst({
        where: {
          docId: fileUuid,
        },
      });
      if (fileRecord == null) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }

      const statusMapping = {
        verify_success: FileStatus.VERIFY_SUCCESS,
        processing: FileStatus.PROCESSING,
        verify_failed: FileStatus.VERIFY_FAILED,
        completed: FileStatus.COMPLETED,
      };

      let updateStatus: FileStatus = 'UPLOADED';
      if (data.status) {
        updateStatus = statusMapping[data.status];
      }

      if (
        ((updateStatus === 'VERIFY_SUCCESS' || updateStatus === 'VERIFY_FAILED') &&
          fileRecord.status !== 'UPLOADED') ||
        (updateStatus === 'COMPLETED' && fileRecord.status !== 'PROCESSING')
      ) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }
      await this.updateFileVerifyResult('modelFile', fileRecord.id, data);
      return fileRecord;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async updateModelFilePiiStatus(body: { piiFileStatus: string; docId: string }) {
    try {
      const data: Record<string, string | number | unknown> = {
        piiFileStatus: body.piiFileStatus,
      };
      await this.prisma.modelFile.update({
        where: { docId: body.docId },
        data,
      });
      // throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      return { status: 200, msg: 'suc updatedModelFile' };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async updateModelfileForMalwareScan(body: {
    status: number;
    docid: string;
    checksum: string;
    rating: string;
  }) {
    try {
      const modelFile = await this.prisma.modelFile.findFirst({
        where: { docId: body.docid },
        include: {
          group: {
            select: {
              env: true,
              id: true,
            },
          },
          model: {
            select: {
              modelId: true,
            },
          },
        },
      });

      if (!modelFile) throw new NotFoundException('ModelFile cannot be found in DB');

      const data = await this.handleMalwareScanResult(body, modelFile);
      data['scanMalwareVersion'] = moment().format('YYYYMMDD');

      if (
        (await this.isClean(modelFile.groupId, body.rating)) &&
        data['scanMalwareStatus'] == ScanMalwareStatus.COMPLETED
      ) {
        if (
          modelFile.status === FileStatus.VERIFY_FAILED ||
          modelFile.status === FileStatus.UPLOADED
        ) {
          data['status'] = FileStatus.VERIFY_SUCCESS;
        }
      } else {
        if (
          modelFile.status === FileStatus.VERIFY_SUCCESS ||
          modelFile.status === FileStatus.UPLOADED
        ) {
          data['status'] = FileStatus.VERIFY_FAILED;
        }
      }

      const updatedModelFile = await this.prisma.modelFile.update({
        where: { docId: body.docid },
        data,
      });

      if (
        updatedModelFile.scanMalwareStatus === ScanMalwareStatus.COMPLETED &&
        (await this.isClean(modelFile.groupId, body.rating))
      ) {
        //after malware scan, need to scan pii and verify
        await this.modelFilePiiScanAndVerify(updatedModelFile, modelFile.group.env);
      }
      return { status: 200, msg: 'suc updatedModelFile' };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  restructureWhereTags(whereTags: unknown) {
    if (!whereTags) return undefined;

    const isNone = typeof whereTags === 'string' && whereTags === 'none';
    if (isNone) return { none: {} };

    const isIn = typeof whereTags === 'object' && 'in' in whereTags;
    if (!isIn) throw new ApiException(ErrorCode.INVALID_TAGS_INPUT, { whereTags });
    if (!Array.isArray(whereTags.in))
      throw new ApiException(ErrorCode.INVALID_TAGS_INPUT, {
        message: 'where.tags should be a array.',
        whereTags,
      });
    if (whereTags.in.length === 0) return undefined;
    if (whereTags.in.length > 0 && whereTags.in.some((tag) => typeof tag !== 'string'))
      throw new ApiException(ErrorCode.INVALID_TAGS_INPUT, {
        message: 'where.tags should be a string array.',
        whereTags,
      });

    return {
      some: {
        LabelEntityType: LabelEntityType.MODEL_FILE,
        Labels: {
          name: { in: whereTags.in },
          labelType: LabelType.TAG,
        },
      },
    };
  }

  malwareCondition(
    condition: ModelFilePermissionButton,
    modelFile: ModelFile,
    overrideFeatureFlag?: GetOverrideFeatureFlagsOrDefaultDTO,
  ): boolean {
    const ratingWhitelist: string[] = overrideFeatureFlag.isEnabled
      ? (overrideFeatureFlag.metaData['values'] as string[]) ?? ['CLEAN']
      : ['CLEAN'];
    if (condition.hasMalware === HasMalware.YES) {
      return !(
        modelFile.errCode?.includes('MS0003') &&
        modelFile.scanMalwareStatus === ScanMalwareStatus.COMPLETED &&
        !ratingWhitelist.includes(modelFile.malwareRating?.toUpperCase())
      );
    } else if (condition.hasMalware === HasMalware.NO) {
      return !(
        !modelFile.errCode?.includes('MS') &&
        modelFile.scanMalwareStatus === ScanMalwareStatus.COMPLETED &&
        ratingWhitelist.includes(modelFile.malwareRating?.toUpperCase())
      );
    } else if (condition.hasMalware === HasMalware.ERROR) {
      return !(
        modelFile.scanMalwareStatus === ScanMalwareStatus.SCANNING_FAILED ||
        modelFile.scanMalwareStatus === ScanMalwareStatus.PENDING
      );
    }
    return true;
  }

  private getUpdatedStatus(modelFile: ModelFile, rating: string, scanMalwareStatus): FileStatus {
    if (modelFile.piiFileStatus == null) {
      // bypass scan pii
      return !this.isClean(modelFile.groupId, rating) ||
        scanMalwareStatus === ScanMalwareStatus.SCANNING_FAILED
        ? FileStatus.VERIFY_FAILED
        : FileStatus.VERIFY_SUCCESS;
    } else {
      if (modelFile.piiFileStatus === PiiFileStatus.COMPLETED) {
        return this.isClean(modelFile.groupId, rating) &&
          modelFile.hasPii === 'NO' &&
          modelFile.hasPromptInjection === 'NO'
          ? FileStatus.VERIFY_SUCCESS
          : FileStatus.VERIFY_FAILED;
      } else if (modelFile.piiFileStatus === PiiFileStatus.SCANNING_FAILED) {
        return FileStatus.VERIFY_FAILED;
      }
    }
    return modelFile.status as FileStatus;
  }

  async addJobToPiiScanQueue(
    s3Path: string,
    env: string,
    docId: string,
    userId: number,
    fileSize: number,
    callbackUrl?: string,
  ) {
    let response: AxiosResponse<any>;
    let url: string;

    const defaultCommonBody = {
      name: docId,
      data: {
        s3Path: s3Path,
        env: env,
        docId: docId,
      },
    };
    if (callbackUrl) {
      defaultCommonBody.data['callbackUrl'] = callbackUrl;
    }

    if (fileSize && fileSize > this.configService.get<number>('splitFileSize', 0)) {
      url =
        this.configService.get<string>('jobQueue.backendPrefix') +
        '/producer/llmguard-scan-large-file/addJob';
    } else {
      url =
        this.configService.get<string>('jobQueue.backendPrefix') +
        '/producer/llmguard-scan-small-file/addJob';
    }
    this.logger.log(
      `[LLMModelsService][addJobToPiiScanQueue] add job to queue request: ${JSON.stringify(
        defaultCommonBody,
      )}`,
    );

    response = await axios.post(url, defaultCommonBody);

    this.logger.log(
      `[LLMModelsService][addJobToPiiScanQueue] add job to queue response: ${JSON.stringify(
        response.data,
      )}`,
    );
    return response;
  }

  async getValidModelFileDocIds(docIds: string[], status: FileStatus[]) {
    const files = await this.prisma.modelFile.findMany({
      where: {
        docId: {
          in: docIds,
        },
        status: {
          in: status,
        },
      },
      select: {
        docId: true,
      },
    });
    return files.map((file) => file.docId);
  }

  async getApprovalModelFileDocIds(docIds: string[]) {
    const files = await this.prisma.modelFile.findMany({
      where: {
        docId: {
          in: docIds,
        },
        isApproved: true,
      },
      select: {
        docId: true,
      },
    });
    return files.map((file) => file.docId);
  }

  async updateChatfileForMalwareScan(body: {
    status: number;
    docid: string;
    checksum: string;
    rating: string;
  }) {
    try {
      let chatFile = await this.prisma.chatFile.findFirst({
        where: { s3Basename: body.docid },
      });
      if (!chatFile) throw new NotFoundException('chatFile cannot be found in DB');

      const data = await this.handleMalwareScanResult(body, chatFile);
      data['scanMalwareVersion'] = moment().format('YYYYMMDD');
      chatFile = await this.prisma.chatFile.update({
        where: { id: chatFile.id },
        data,
      });
      //if malware scan is completed and the file is clean, then start pii scan and verify file
      if (
        chatFile.scanMalwareStatus === ScanMalwareStatus.COMPLETED &&
        chatFile.malwareRating === 'Clean'
      ) {
        const s3FileKey = this.chatFilesService.getS3FileKey(
          chatFile.groupId,
          chatFile.userId,
          chatFile.s3Basename,
          ChatFileType.QUESTION,
        );
        //pii scan
        await this.chatFilePiiScan(chatFile, s3FileKey);
        //verify file
        if (chatFile.verifyStatus === null) {
          const callBackUrl = `/v1/embeddings/verify/chatFile/${chatFile.id}`;
          await this.verifyFile('modelFile', s3FileKey, chatFile, callBackUrl, chatFile.userId);
        }
      }
      return { status: 200, msg: 'suc updatedChatFile' };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  private async handleMalwareScanResult(
    body: {
      status: number;
      docid: string;
      checksum: string;
      rating: string;
    },
    file: ModelFile | ChatFile,
  ) {
    const data: Record<string, string | number | unknown> = {
      malwareRating: body.rating,
    };
    if (body.status === 0) {
      data['scanMalwareStatus'] = ScanMalwareStatus.COMPLETED;
      if (!(await this.isClean(file.groupId, body.rating))) {
        data['errCode'] = file.errCode ? `${file.errCode}, MS0003` : 'MS0003';
      }
    } else {
      data['scanMalwareStatus'] = ScanMalwareStatus.SCANNING_FAILED;
      data['errCode'] = file.errCode ? `${file.errCode}, MS0002` : 'MS0002';
    }
    return data;
  }

  async chatFilePiiScan(fileRecord: ChatFile, s3FileKey: string) {
    const byPassPiiScan = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.BYPASS_SCAN_PII,
    );
    const piiSupportFormat = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.PII_SCAN_SUPPORT_FORMAT,
    );
    if (
      byPassPiiScan?.isEnabled &&
      fileRecord.piiFileStatus === null &&
      fileRecord.hasPiiScanSkip !== true
    ) {
      await this.chatFilesService.updateChatFileSkipPiiScan(fileRecord.id, true);
    }
    if (byPassPiiScan?.isEnabled || fileRecord.piiFileStatus !== null) {
      return;
    }
    if (piiSupportFormat?.isEnabled) {
      const fileExtension = fileRecord.filename.split('.').pop();
      const allowedFileExtensions = (piiSupportFormat?.metaData?.['values'] as string[]) ?? [];
      if (Array.isArray(allowedFileExtensions) && !allowedFileExtensions.includes(fileExtension)) {
        return;
      }
    }
    const group = await this.prisma.group.findUnique({
      where: { id: fileRecord.groupId },
    });
    // add job to Queue .
    const callbackUrl = '/v1/llm-models/updateChatFilePiiScan';
    const response = await this.addJobToPiiScanQueue(
      s3FileKey,
      group.env,
      fileRecord.s3Basename,
      fileRecord.userId,
      fileRecord.fileSize,
      callbackUrl,
    );

    if (
      typeof response.status === 'number' &&
      [200, 201].includes(response.status) &&
      response.data.id !== undefined
    ) {
      await this.prisma.chatFile.update({
        where: { id: fileRecord.id },
        data: {
          jobId: response.data.id,
        },
      });
    }
  }

  piiScanResultHandler(
    body: {
      piiFileStatus: string;
      docId: string;
      s3path?: string;
      status?: string;
      detectedPii?: string;
      hasPII?: string;
      error?: string;
      errCode?: string;
      hasPromptInjection?: string;
    },
    file: ModelFile | ChatFile,
  ) {
    const data: Record<string, string | number | unknown> = {
      status: body.status,
      piiFileStatus: body.piiFileStatus,
      hasPii: body.hasPII,
      errorMsg: null,
      errCode: null,
      detectedPii: null,
      fullScanReportPath: null,
      fullScanReportCreatedAt: null,
      fullScanReportUpdatedAt: null,
      hasPromptInjection: body.hasPromptInjection,
      fullScanReportVersion: this.configService.get<number>('llmGuard.fullScanPiiVersion'),
    };

    if (body.piiFileStatus === 'COMPLETED') {
      if (body.status === 'VERIFY_FAILED') {
        data['fullScanReportPath'] = body.s3path;
        data['errorMsg'] = body.error;
        data['detectedPii'] = body.detectedPii;
        data['hasPromptInjection'] = body.hasPromptInjection;
        data['fullScanReportUpdatedAt'] = new Date();
        if (!file.fullScanReportCreatedAt) {
          data['fullScanReportCreatedAt'] = new Date();
        }
      }
      if (file.errCode?.includes('MS')) {
        const msErrorCode = file.errCode.match(/MS\d{4}/)[0];
        if (msErrorCode) {
          data['errCode'] = msErrorCode;
        }
      }
    } else if (body.piiFileStatus === 'SCANNING_FAILED') {
      data['errorMsg'] = body.error;
      data['errCode'] = file.errCode ? `${file.errCode}, ${body.errCode}` : body.errCode;
    }
    return data;
  }

  async updateFilePiiScanResult(
    fileType: 'modelFile' | 'chatFile',
    body: {
      piiFileStatus: string;
      docId: string;
      s3path?: string;
      status?: string;
      detectedPii?: string;
      hasPII?: string;
      error?: string;
      errCode?: string;
      hasPromptInjection?: string;
    },
  ) {
    let prismaModel;
    prismaModel = fileType === 'modelFile' ? this.prisma.modelFile : this.prisma.chatFile;
    try {
      let where = {};
      if (fileType === 'chatFile') {
        where = { s3Basename: body.docId };
      } else if (fileType === 'modelFile') {
        where = { docId: body.docId };
      }

      const file = await prismaModel.findFirst({
        where,
      });
      if (!file) throw new NotFoundException('File cannot be found in DB');
      const data = this.piiScanResultHandler(body, file);

      const inputData: Prisma.ChatFileUpdateInput | Prisma.ModelFileUpdateInput = {
        piiFileStatus: data['piiFileStatus'],
        hasPii: data['hasPii'],
        errorMsg: data['errorMsg'],
        errCode: data['errCode'],
        detectedPii: data['detectedPii'],
        fullScanReportPath: data['fullScanReportPath'],
        fullScanReportCreatedAt: data['fullScanReportCreatedAt'],
        fullScanReportUpdatedAt: data['fullScanReportUpdatedAt'],
        hasPromptInjection: data['hasPromptInjection'],
        fullScanReportVersion: data['fullScanReportVersion'],
      };

      await prismaModel.update({
        where: { id: file.id },
        data: inputData,
      });
      return { status: 200, msg: 'suc updatedChatFile' };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async updateFileVerifyResult(
    fileType: 'modelFile' | 'chatFile',
    fileId: number,
    data: { status: string; errCode: string; error: string },
  ) {
    let prismaModel;
    prismaModel = fileType === 'modelFile' ? this.prisma.modelFile : this.prisma.chatFile;
    const file = await prismaModel.findFirst({
      where: { id: fileId },
    });
    if (!file) throw new NotFoundException(`${fileType} cannot be found in DB`);

    const updateData: any = {
      verifyStatus: VerifyStatus.VERIFY_SUCCESS,
    };
    if (data.status != 'verify_success') {
      updateData.verifyStatus = VerifyStatus.VERIFY_FAILED;
      updateData.verifyErrCode = data.errCode;
      updateData.verifyErrorMsg = data.error || 'unknown error';
    }
    await prismaModel.update({
      where: { id: fileId },
      data: updateData,
    });
    return { status: 200, msg: 'suc updatedFile' };
  }

  async verifyFile(
    fileType: 'modelFile' | 'chatFile',
    s3FileKey: string,
    file: ChatFile | ModelFile,
    callbackUrl: string,
    userId: number,
  ) {
    let prismaModel;
    prismaModel = fileType === 'modelFile' ? this.prisma.modelFile : this.prisma.chatFile;
    const group = await this.prisma.group.findUnique({
      where: { id: file.groupId },
      include: {
        llmModel: {
          select: { modelId: true },
        },
      },
    });
    const queueMessage = {
      s3_bucket: this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`),
      s3_key: s3FileKey,
      s3_env: group.env.toLowerCase(),
      group_id: group.id,
      user_id: userId,
      callbackUrl: callbackUrl,
    };
    this.logger.log(`[addMessageToVerifySqsQueue] queueMessage: ${JSON.stringify(queueMessage)}`);
    await this.sqsService.sendMessage({
      MessageGroupId: file.groupId.toString(),
      MessageDeduplicationId: s3FileKey,
      QueueUrl: this.configService.get<string>(`queue.dataProcessQueueUrl.${group.env}`),
      MessageBody: JSON.stringify(queueMessage),
      MessageAttributes: {
        model_id: {
          DataType: 'String',
          StringValue: group.llmModel.modelId,
        },
        env: {
          DataType: 'String',
          StringValue: group.env.toLowerCase(),
        },
        verify: {
          DataType: 'String',
          StringValue: 'True',
        },
      },
    });
    await prismaModel.update({
      where: { id: file.id },
      data: {
        verifyStatus: VerifyStatus.VERIFYING,
      },
    });
  }

  async modelFileMalwareScan(fileRecord: ModelFile) {
    const byPassScanMalware = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.BYPASS_SCAN_MALWARE,
    );

    const group = await this.prisma.group.findUnique({
      where: { id: fileRecord.groupId },
    });

    if (!byPassScanMalware.isEnabled) {
      //scan malware
      if (
        fileRecord.scanMalwareStatus === null ||
        fileRecord.scanMalwareStatus === ScanMalwareStatus.PENDING
      ) {
        this.logger.log({
          Action: 'Going to fortiSandbox to scan malware',
          'group.env': group.env,
          'model.modelId': fileRecord.modelId,
          'doc.s3Path': fileRecord.s3Path,
          groupId: fileRecord.groupId,
          fileUuid: fileRecord.docId,
        });
        await this.fortiSanboxService.scanMalware(
          fileRecord.docId,
          fileRecord,
          group.env,
          this.configService.get<string>('scanMalware.callbackUrl'),
        );
      } else {
        await this.modelFilePiiScanAndVerify(fileRecord, group.env);
      }
    } else {
      if (fileRecord.status === FileStatus.UPLOADED) {
        await this.prisma.modelFile.update({
          where: { id: fileRecord.id },
          data: {
            status: FileStatus.VERIFY_SUCCESS,
          },
        });
      }
      await this.modelFilePiiScanAndVerify(fileRecord, group.env);
    }
  }

  async modelFilePiiScanAndVerify(fileRecord: ModelFile, env: string) {
    this.modelFilePiiScan(fileRecord, env).catch((error: Error) => {
      this.logger.error(
        `modelFilePiiScanAndVerify modelFilePiiScan error :${error.message} `,
        error.stack,
      );
    });
    if (fileRecord.verifyStatus === null) {
      const callBackUrl = `/v1/embeddings/verify/modelFile/${fileRecord.id}`;
      await this.verifyFile(
        'modelFile',
        fileRecord.s3Path,
        fileRecord,
        callBackUrl,
        fileRecord.uploaderId,
      );
    }
  }

  async modelFilePiiScan(fileRecord: ModelFile, env: string) {
    const byPassScanPII = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.BYPASS_SCAN_PII,
    );

    if (!byPassScanPII.isEnabled && fileRecord.piiFileStatus === null) {
      // add job to Queue .
      const callbackUrl = '/v1/llm-models/updateFullScanByBullMq';
      const response = await this.addJobToPiiScanQueue(
        fileRecord.s3Path,
        env,
        fileRecord.docId,
        fileRecord.uploaderId,
        fileRecord.fileSize,
        callbackUrl,
      );
      if (
        typeof response.status === 'number' &&
        [200, 201].includes(response.status) &&
        response.data.id !== undefined
      ) {
        await this.prisma.modelFile.update({
          where: { id: fileRecord.id },
          data: {
            jobId: response.data.id,
          },
        });
      }
      this.logger.log(
        `[modelFilePiiScan] add job to pii scan queue response: ${JSON.stringify(response.data)}`,
      );
    }
  }

  private async isClean(groupId: number, rating: string): Promise<boolean> {
    const overrideFeatureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.BOT_MALWARE_RATING_WHITELIST,
    );
    const ratingWhitelist: string[] = overrideFeatureFlag.isEnabled
      ? (overrideFeatureFlag.metaData['values'] as string[]) ?? ['CLEAN']
      : ['CLEAN'];
    return ratingWhitelist.includes(rating.toUpperCase());
  }

  private async getModelFileEntity(
    modelFile: ModelFile,
    buttonCoditionList: ModelFilePermissionButton[],
    isRequireSecondaryApproval: boolean,
    byPassScanMalware: boolean,
    overrideFeatureFlag: GetOverrideFeatureFlagsOrDefaultDTO,
  ): Promise<ModelFile> {
    const ratingWhitelist: string[] = overrideFeatureFlag.isEnabled
      ? (overrideFeatureFlag.metaData['values'] as string[]) ?? ['CLEAN']
      : ['CLEAN'];
    // const isClean = ratingWhitelist.includes(modelFile.malwareRating.toUpperCase());

    let isClean = null;
    if (modelFile.malwareRating) {
      isClean = ratingWhitelist.includes(modelFile.malwareRating.toUpperCase());
    }

    // Determine which button permissions the current modelfile has based on the role
    const buttonList = this.getButtonList(
      buttonCoditionList,
      modelFile,
      isRequireSecondaryApproval,
      byPassScanMalware,
      overrideFeatureFlag,
    );

    const entity = {
      ...modelFile,
      buttonList: buttonList,
      statusExt: modelFile.status ? (modelFile.status as string) : '',
      isClean,
      position: null,
    };
    if (modelFile.jobId) {
      const queueName =
        modelFile.fileSize > this.configService.get<number>('splitFileSize', 0)
          ? 'llmguard-scan-large-file'
          : 'llmguard-scan-small-file';
      entity.position = await this.redis.getJobPositonInQueue(queueName, 'wait', modelFile.jobId);
    }
    if (
      modelFile.status === FileStatus.VERIFY_FAILED ||
      modelFile.status === FileStatus.VERIFY_SUCCESS
    ) {
      if (
        modelFile.status === FileStatus.VERIFY_FAILED ||
        ScanMalwareStatus.SCANNING_FAILED === modelFile.scanMalwareStatus ||
        (ScanMalwareStatus.COMPLETED == modelFile.scanMalwareStatus && !isClean)
      ) {
        entity.statusExt = 'FAIL_TO_UPLOAD';
      } else if (
        modelFile.status === FileStatus.VERIFY_SUCCESS &&
        modelFile.isApproved &&
        isRequireSecondaryApproval
      ) {
        entity.statusExt = 'PENDING_2ND_APPROVAL';
      }
    }
    return entity;
  }

  async downloadSpecifiedfullScanPiiFile(s3Path: string, groupId: number) {
    try {
      const group = await this.prisma.group.findUnique({
        where: { id: groupId },
      });

      if (!group) {
        throw new NotFoundException(`Group with id ${groupId} not found`);
      }

      return this.s3Service.getObjectUnsafe(
        this.configService.get<string>(`s3.piiReportBuckets.${group.env}`),
        s3Path,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  public async getAdminPublicLLMModels(pageWhere: PublicLLMModelWhereDto) {
    const where = {
      ...pageWhere.where,
      group: {
        env: Environment.PROD,
        isDeprecated: false,
      },
      makeLiveToPublic: true,
    };
    const bots = await this.prisma.lLMModel.findMany({
      include: {
        ...(this.labelsService.getLabelsPrismaQuery('LLM_MODEL') as any),
        group: {
          include: {
            createdBy: {
              include: {
                emails: true,
              },
            },
          },
        },
      },
      skip: pageWhere?.skip,
      take: pageWhere?.take,
      orderBy: {
        publicOrder: 'asc',
      },
      where,
    });
    const count = await this.prisma.lLMModel.count({ where });
    const list = bots.map((item) => ({
      ...this.labelsService.formatLabelsAndCategoriesData(item),
      description: (item?.['group'] as any)?.description,
      businessUnit: (item?.['group'] as any)?.businessUnit,
      creator: (item?.['group'] as any)?.createdBy?.name,
      email: (item?.['group'] as any)?.createdBy?.emails[0]?.email,
      group: undefined,
    }));
    return { list, count };
  }

  /**
   * @description the public bot just will show the env PROD list ,
   * @param skip
   * @param take
   * @returns
   */
  public async getPublicLLMModels(
    req: UserRequest,
    skip?: number,
    take?: number,
    where?: Record<string, any>,
  ) {
    const _condition = this.labelsService.formatLabelsAndCategoriesFilter('LLM_MODEL', where);
    const condition = {
      ..._condition,
      group: {
        env: 'PROD',
        isDeprecated: false,
      },
      active: true,
      makeLiveToPublic: true,
    } as Prisma.LLMModelWhereInput;
    const bots = await this.prisma.lLMModel.findMany({
      include: {
        ...(this.labelsService.getLabelsPrismaQuery('LLM_MODEL') as any),
        group: {
          include: {
            createdBy: true,
            memberships: {
              include: {
                Role: true,
              },
              where: {
                userId: req.user.id,
              },
            },
          },
        },
      },
      where: condition,
      skip,
      take,
      orderBy: {
        publicOrder: 'asc',
      },
    });
    const count = await this.prisma.lLMModel.count({
      where: condition,
    });
    const botsIds = bots.map((item) => item.id);
    const botsBookmarkCount = await this.prisma.userBookmark.groupBy({
      by: ['entityType', 'entityId'],
      where: {
        entityType: UserBookmarkEntityType.PUBLIC_BOT,
        entityId: { in: botsIds },
      },
      _count: { id: true },
    });
    const groupIds = bots.map((item) => item.groupId);
    const publicBotUsedCount = await this.prisma.chatSession.groupBy({
      by: ['chatSessionType', 'groupId'],
      where: {
        chatSessionType: ChatSessionType.PUBLIC,
        groupId: { in: groupIds },
      },
      _count: { groupId: true },
    });
    const list = bots.map((item) => ({
      ...this.labelsService.formatLabelsAndCategoriesData(item),
      group: {
        Role: item?.['group']?.['memberships']?.[0]?.Role,
        ...item?.['group'],
        memberships: undefined,
        createdBy: undefined,
      },
      creator: item?.['group']?.['createdBy']?.name,
      bookmarkedNo: botsBookmarkCount.find((_item) => _item.entityId == item.id)?._count?.id ?? 0,
      used: publicBotUsedCount.find((_item) => _item.groupId == item.groupId)?._count?.groupId ?? 0,
    }));
    return { list, count };
  }

  /**
   * Checks if the specified group is of type BOT.
   *
   * @param groupId - The ID of the group to check.
   * @returns The group object if it is of type BOT.
   * @throws {ApiException} If the group is not of type BOT.
   */
  public async checkIsBot(groupId: number) {
    const group = await this.groupService.getGroup(groupId, {
      include: {
        llmModel: true,
        memberships: {
          include: {
            Role: true,
          },
        },
      } as any,
    });
    if (group.groupType != GroupType.BOT) {
      throw new ApiException(ErrorCode.INVAILD_PUBLIC_GROUP_TYPE);
    }
    return group;
  }

  public async getPublicBots(
    req: UserRequest,
    where?: PublicLLMModelWhereDto['where'],
    groupId?: number,
    skip?: number,
    take?: number,
  ) {
    if (groupId) {
      delete where?.categories;
      delete where?.labels;
      const group = await this.checkIsBot(groupId);
      const llmModel = (group as any).llmModel;
      delete (group as any)?.llmModel;
      llmModel['group'] = {
        Role: group?.['memberships']?.[0]?.Role,
        ...group,
        memberships: undefined,
      };
      return { list: [llmModel], count: 1 };
    }
    return this.getPublicLLMModels(req, skip, take, where);
  }

  public async updatePublicBot(
    llmModelId: number,
    userReq: UserRequest,
    data: UpdatePublicLlmModelDto,
  ) {
    const lLMModel = await this.prisma.lLMModel.findFirst({ where: { id: llmModelId } });
    const isPublicBot = lLMModel && lLMModel.makeLiveToPublic;
    if (!isPublicBot) {
      throw new ApiException(ErrorCode.PUBLIC_BOT_NOT_FOUND);
    }
    const { needRemoveIds, createEntityLabels, createLabels } =
      this.labelsService.formatPatchLabelsDto(data as any, userReq, 'LLM_MODEL', llmModelId);
    const updated = await this.prisma.$transaction(async (tx) => {
      await this.labelsService.patchUpdateWithTransaction(
        { needRemoveIds, createEntityLabels, createLabels },
        tx,
        'LLM_MODEL',
        llmModelId,
      );
      const update = await tx.lLMModel.update({
        where: {
          id: llmModelId,
        },
        data: {
          publicOrder: data.publicOrder,
          publicBotGuideDetails: data.publicBotGuideDetails,
          official: data.official,
        },
      });
      return update;
    });
    return updated;
  }
  public async changePublicBotStatus(groupId: number, status: boolean) {
    const group = await this.groupService.getGroup(groupId, {});
    const updateGroupIds = [group.id];
    if (group.id == group.pairId) {
      const groupTestEnv = await this.prisma.group.findFirst({
        where: {
          pairId: group.id,
        },
      });
      updateGroupIds.push(groupTestEnv.id);
    }
    await this.prisma.lLMModel.updateMany({
      where: {
        groupId: {
          in: updateGroupIds,
        },
      },
      data: {
        makeLiveToPublic: status,
      },
    });
    return { success: true };
  }

  public async getModelFilesByAutoSelectFilters(
    groupId: number,
    fileTypes: string[],
    fileTags: string[],
  ) {
    if (!(fileTypes?.length > 0 || fileTags?.length > 0)) {
      return [];
    }

    return this.prisma.modelFile.findMany({
      where: {
        groupId,
        status: FileStatus.COMPLETED,
        filetype: {
          in: fileTypes?.length > 0 ? fileTypes : undefined,
        },
        tags:
          fileTags?.length > 0
            ? {
                some: {
                  LabelEntityType: LabelEntityType.MODEL_FILE,
                  Labels: {
                    name: {
                      in: fileTags,
                    },
                    labelType: LabelType.TAG,
                  },
                },
              }
            : undefined,
      },
    });
  }
}
