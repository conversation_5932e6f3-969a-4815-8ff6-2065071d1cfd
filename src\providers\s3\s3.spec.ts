import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { S3Service } from './s3.service';
import { DeepMockProxy, mockDeep } from 'jest-mock-extended';
import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import {
  S3Client,
  GetObjectCommand,
  HeadObjectCommand,
  DeleteObjectCommand,
  DeleteObjectsCommand,
  CopyObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { createPresignedPost } from '@aws-sdk/s3-presigned-post';
import { Upload } from '@aws-sdk/lib-storage';

// Mock AWS SDK modules
jest.mock('@aws-sdk/client-s3');
jest.mock('@aws-sdk/s3-request-presigner');
jest.mock('@aws-sdk/s3-presigned-post');
jest.mock('@aws-sdk/lib-storage');

const moduleMocker = new ModuleMocker(global);

describe('S3Service', () => {
  let s3Service: S3Service;
  let configService: DeepMockProxy<ConfigService>;
  let mockS3Client: jest.Mocked<S3Client>;
  let mockLogger: DeepMockProxy<Logger>;

  const mockS3Config = {
    region: 'ap-east-1',
    endpoint: '', //https://s3.amazonaws.com
    accessKeyId: '********************',
    secretAccessKey: 'uTX/trfd3EHjoeAx7OhqJntXl57+1jpNG89bckE/',
  };

  beforeEach(async () => {
    configService = mockDeep<ConfigService>();
    mockS3Client = {
      send: jest.fn().mockResolvedValue({}),
    } as any;
    mockLogger = mockDeep<Logger>();

    // Mock S3Client constructor
    (S3Client as jest.MockedClass<typeof S3Client>).mockImplementation(() => mockS3Client);

    configService.get.mockReturnValue(mockS3Config);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        S3Service,
        {
          provide: ConfigService,
          useValue: configService,
        },
      ],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();

    s3Service = module.get<S3Service>(S3Service);
    
    // Replace the logger with our mock
    (s3Service as any).logger = mockLogger;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize S3Client with correct configuration', () => {
      expect(configService.get).toHaveBeenCalledWith('s3');
      expect(S3Client).toHaveBeenCalledWith({
        region: mockS3Config.region,
        endpoint: mockS3Config.endpoint,
      });
    });

    it('should initialize S3Client without endpoint when not provided', () => {
      const configWithoutEndpoint = { ...mockS3Config };
      delete configWithoutEndpoint.endpoint;
      configService.get.mockReturnValue(configWithoutEndpoint);

      new S3Service(configService);

      expect(S3Client).toHaveBeenCalledWith({
        region: mockS3Config.region,
      });
    });
  });


  describe('upload', () => {
    it('should upload file successfully', async () => {
      const name = 'test-file.txt';
      const originalFilename = 'original.txt';
      const body = Buffer.from('test content');
      const filetype = 'text/plain';
      const groupId = 123;
      const bucket = 'test-bucket';
      const publicRead = true;

      const mockUpload = {
        done: jest.fn().mockResolvedValue({ Location: 'https://s3.amazonaws.com/test-bucket/test-file.txt' }),
      };

      (Upload as jest.MockedClass<typeof Upload>).mockImplementation(() => mockUpload as any);

      const result = await s3Service.upload(name, originalFilename, body, filetype, groupId, bucket, publicRead);

      expect(Upload).toHaveBeenCalledWith({
        client: mockS3Client,
        params: {
          Bucket: bucket,
          Key: name,
          Body: body,
          ACL: 'public-read',
          ContentType: filetype,
          Metadata: {
            filename: Buffer.from(originalFilename).toString('base64'),
            groupid: groupId.toString(),
          },
        },
      });
      expect(result).toEqual({ Location: 'https://s3.amazonaws.com/test-bucket/test-file.txt' });
    });

    it('should upload file without public read when publicRead is false', async () => {
      const name = 'test-file.txt';
      const originalFilename = 'original.txt';
      const body = Buffer.from('test content');
      const filetype = 'text/plain';
      const groupId = 123;
      const bucket = 'test-bucket';
      const publicRead = false;

      const mockUpload = {
        done: jest.fn().mockResolvedValue({ Location: 'https://s3.amazonaws.com/test-bucket/test-file.txt' }),
      };

      (Upload as jest.MockedClass<typeof Upload>).mockImplementation(() => mockUpload as any);

      await s3Service.upload(name, originalFilename, body, filetype, groupId, bucket, publicRead);

      expect(Upload).toHaveBeenCalledWith({
        client: mockS3Client,
        params: {
          Bucket: bucket,
          Key: name,
          Body: body,
          ACL: undefined,
          ContentType: filetype,
          Metadata: {
            filename: Buffer.from(originalFilename).toString('base64'),
            groupid: groupId.toString(),
          },
        },
      });
    });

    it('should throw error when upload fails', async () => {
      const name = 'test-file.txt';
      const originalFilename = 'original.txt';
      const body = Buffer.from('test content');
      const filetype = 'text/plain';
      const groupId = 123;
      const bucket = 'test-bucket';
      const error = new Error('Upload failed');

      const mockUpload = {
        done: jest.fn().mockRejectedValue(error),
      };

      (Upload as jest.MockedClass<typeof Upload>).mockImplementation(() => mockUpload as any);

      await expect(s3Service.upload(name, originalFilename, body, filetype, groupId, bucket)).rejects.toThrow(error);
    });
  });

  describe('getContentLength', () => {
    it('should return content length successfully', async () => {
      const bucket = 'test-bucket';
      const name = 'test-file.txt';
      const expectedLength = 1024;

      (mockS3Client.send as jest.Mock).mockResolvedValue({ ContentLength: expectedLength });

      const result = await s3Service.getContentLength(bucket, name);

      expect(mockS3Client.send).toHaveBeenCalledWith(expect.any(GetObjectCommand));
      expect(result).toBe(expectedLength);
    });

    it('should throw error and log when getContentLength fails', async () => {
      const bucket = 'test-bucket';
      const name = 'test-file.txt';
      const error = new Error('AWS Error');

      (mockS3Client.send as jest.Mock).mockRejectedValue(error);

      await expect(s3Service.getContentLength(bucket, name)).rejects.toThrow();
      expect(mockLogger.error).toHaveBeenCalledWith(error, 'Failed to get s3 object err');
    });
  });

  describe('head', () => {
    it('should return head object response successfully', async () => {
      const bucket = 'test-bucket';
      const name = 'test-file.txt';
      const expectedResponse = { ContentType: 'text/plain', ContentLength: 1024 };

      (mockS3Client.send as jest.Mock).mockResolvedValue(expectedResponse);

      const result = await s3Service.head(bucket, name);

      expect(mockS3Client.send).toHaveBeenCalledWith(expect.any(HeadObjectCommand));
      expect(result).toBe(expectedResponse);
    });
  });

  describe('get', () => {
    it('should return get object response successfully', async () => {
      const bucket = 'test-bucket';
      const name = 'test-file.txt';
      const expectedResponse = { Body: 'file content', ContentType: 'text/plain' };

      (mockS3Client.send as jest.Mock).mockResolvedValue(expectedResponse);

      const result = await s3Service.get(bucket, name);

      expect(mockS3Client.send).toHaveBeenCalledWith(expect.any(GetObjectCommand));
      expect(result).toBe(expectedResponse);
    });
  });

  describe('getFileObject', () => {
    it('should return file object successfully', async () => {
      const bucket = 'test-bucket';
      const name = 'test-file.txt';
      const expectedResponse = { Body: 'file content', ContentType: 'text/plain' };

      (mockS3Client.send as jest.Mock).mockResolvedValue(expectedResponse);

      const result = await s3Service.getFileObject(bucket, name);

      expect(mockS3Client.send).toHaveBeenCalledWith(expect.any(GetObjectCommand));
      expect(result).toBe(expectedResponse);
    });
  });

  describe('getObjectUnsafe', () => {
    it('should return object unsafe successfully', async () => {
      const bucket = 'test-bucket';
      const key = 'test-file.txt';
      const expectedResponse = { Body: 'file content', ContentType: 'text/plain' };

      (mockS3Client.send as jest.Mock).mockResolvedValue(expectedResponse);

      const result = await s3Service.getObjectUnsafe(bucket, key);

      expect(mockS3Client.send).toHaveBeenCalledWith(expect.any(GetObjectCommand));
      expect(result).toBe(expectedResponse);
    });
  });

  describe('delete', () => {
    it('should delete object successfully', async () => {
      const bucket = 'test-bucket';
      const key = 'test-file.txt';
      const expectedResponse = { DeleteMarker: true };

      (mockS3Client.send as jest.Mock).mockResolvedValue(expectedResponse);

      const result = await s3Service.delete(bucket, key);

      expect(mockS3Client.send).toHaveBeenCalledWith(expect.any(DeleteObjectCommand));
      expect(result).toBe(expectedResponse);
    });

    it('should throw error when delete fails', async () => {
      const bucket = 'test-bucket';
      const key = 'test-file.txt';
      const error = new Error('Delete failed');

      (mockS3Client.send as jest.Mock).mockRejectedValue(error);

      await expect(s3Service.delete(bucket, key)).rejects.toThrow(error);
    });
  });

  describe('deleteMany', () => {
    it('should delete multiple objects successfully', async () => {
      const bucket = 'test-bucket';
      const keys = ['file1.txt', 'file2.txt', 'file3.txt'];
      const expectedResponse = { Deleted: [{ Key: 'file1.txt' }, { Key: 'file2.txt' }, { Key: 'file3.txt' }] };

      (mockS3Client.send as jest.Mock).mockResolvedValue(expectedResponse);

      const result = await s3Service.deleteMany(bucket, keys);

      expect(mockS3Client.send).toHaveBeenCalledWith(expect.any(DeleteObjectsCommand));
      expect(result).toBe(expectedResponse);
    });

    it('should throw error when deleteMany fails', async () => {
      const bucket = 'test-bucket';
      const keys = ['file1.txt', 'file2.txt'];
      const error = new Error('Delete many failed');

      (mockS3Client.send as jest.Mock).mockRejectedValue(error);

      await expect(s3Service.deleteMany(bucket, keys)).rejects.toThrow(error);
    });
  });

  describe('copyFileObject', () => {
    it('should copy file object successfully', async () => {
      const sourceBucket = 'source-bucket';
      const sourcePath = 'source/file.txt';
      const targetBucket = 'target-bucket';
      const targetKey = 'target/file.txt';
      const expectedResponse = { CopyObjectResult: { ETag: '"abc123"' } };

      (mockS3Client.send as jest.Mock).mockResolvedValue(expectedResponse);

      const result = await s3Service.copyFileObject(sourceBucket, sourcePath, targetBucket, targetKey);

      expect(mockS3Client.send).toHaveBeenCalledWith(expect.any(CopyObjectCommand));
      expect(result).toBe(expectedResponse);
    });
  });

  describe('isExistedObject', () => {
    it('should return true when object exists', async () => {
      const bucket = 'test-bucket';
      const key = 'test-file.txt';

      (mockS3Client.send as jest.Mock).mockResolvedValue({ ContentType: 'text/plain' });

      const result = await s3Service.isExistedObject(bucket, key);

      expect(mockS3Client.send).toHaveBeenCalledWith(expect.any(HeadObjectCommand));
      expect(result).toBe(true);
    });

    it('should return false when object does not exist', async () => {
      const bucket = 'test-bucket';
      const key = 'non-existent-file.txt';

      (mockS3Client.send as jest.Mock).mockRejectedValue(new Error('Not found'));

      const result = await s3Service.isExistedObject(bucket, key);

      expect(result).toBe(false);
    });
  });

  describe('getContentType', () => {
    it('should return content type successfully', async () => {
      const bucket = 'test-bucket';
      const key = 'test-file.txt';
      const expectedContentType = 'text/plain';

      (mockS3Client.send as jest.Mock).mockResolvedValue({ ContentType: expectedContentType });

      const result = await s3Service.getContentType(bucket, key);

      expect(mockS3Client.send).toHaveBeenCalledWith(expect.any(HeadObjectCommand));
      expect(result).toBe(expectedContentType);
    });

    it('should throw error and log when getContentType fails', async () => {
      const bucket = 'test-bucket';
      const key = 'test-file.txt';
      const error = new Error('AWS Error');

      (mockS3Client.send as jest.Mock).mockRejectedValue(error);

      await expect(s3Service.getContentType(bucket, key)).rejects.toThrow();
      expect(mockLogger.error).toHaveBeenCalledWith(error, 'Failed to get s3 object err');
    });
  });

  describe('uploadAndCacheControl', () => {
    it('should upload file with cache control successfully', async () => {
      const name = 'test-file.txt';
      const originalFilename = 'original.txt';
      const body = Buffer.from('test content');
      const filetype = 'text/plain';
      const groupId = 123;
      const cacheControl = 'max-age=3600';
      const bucket = 'test-bucket';
      const publicRead = true;

      const mockUpload = {
        done: jest.fn().mockResolvedValue({ Location: 'https://s3.amazonaws.com/test-bucket/test-file.txt' }),
      };

      (Upload as jest.MockedClass<typeof Upload>).mockImplementation(() => mockUpload as any);

      const result = await s3Service.uploadAndCacheControl(
        name,
        originalFilename,
        body,
        filetype,
        groupId,
        cacheControl,
        bucket,
        publicRead
      );

      expect(Upload).toHaveBeenCalledWith({
        client: mockS3Client,
        params: {
          Bucket: bucket,
          Key: name,
          Body: body,
          ACL: 'public-read',
          ContentType: filetype,
          CacheControl: cacheControl,
          Metadata: {
            filename: Buffer.from(originalFilename).toString('base64'),
            groupid: groupId.toString(),
          },
        },
      });
      expect(result).toEqual({ Location: 'https://s3.amazonaws.com/test-bucket/test-file.txt' });
    });

    it('should upload file with cache control without public read when publicRead is false', async () => {
      const name = 'test-file.txt';
      const originalFilename = 'original.txt';
      const body = Buffer.from('test content');
      const filetype = 'text/plain';
      const groupId = 123;
      const cacheControl = 'max-age=3600';
      const bucket = 'test-bucket';
      const publicRead = false;

      const mockUpload = {
        done: jest.fn().mockResolvedValue({ Location: 'https://s3.amazonaws.com/test-bucket/test-file.txt' }),
      };

      (Upload as jest.MockedClass<typeof Upload>).mockImplementation(() => mockUpload as any);

      await s3Service.uploadAndCacheControl(
        name,
        originalFilename,
        body,
        filetype,
        groupId,
        cacheControl,
        bucket,
        publicRead
      );

      expect(Upload).toHaveBeenCalledWith({
        client: mockS3Client,
        params: {
          Bucket: bucket,
          Key: name,
          Body: body,
          ACL: undefined,
          ContentType: filetype,
          CacheControl: cacheControl,
          Metadata: {
            filename: Buffer.from(originalFilename).toString('base64'),
            groupid: groupId.toString(),
          },
        },
      });
    });

    it('should throw error when uploadAndCacheControl fails', async () => {
      const name = 'test-file.txt';
      const originalFilename = 'original.txt';
      const body = Buffer.from('test content');
      const filetype = 'text/plain';
      const groupId = 123;
      const cacheControl = 'max-age=3600';
      const bucket = 'test-bucket';
      const error = new Error('Upload failed');

      const mockUpload = {
        done: jest.fn().mockRejectedValue(error),
      };

      (Upload as jest.MockedClass<typeof Upload>).mockImplementation(() => mockUpload as any);

      await expect(
        s3Service.uploadAndCacheControl(name, originalFilename, body, filetype, groupId, cacheControl, bucket)
      ).rejects.toThrow(error);
    });
  });
});
