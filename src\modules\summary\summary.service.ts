import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  Environment,
  FeatureFlag,
  FileEntityType,
  FileHistory,
  FileHistoryStatus,
  FileType,
  FlowBotStatus,
  Group,
  GroupType,
  Prisma,
  Summary,
  SummaryCallingType,
  SummaryKeyType,
} from '@prisma/client';
import { addDays } from 'date-fns';
import moment from 'moment';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { ElasticSearchService } from 'src/providers/elasticsearch/elasticsearch.service';
import { MailService } from 'src/providers/mail/mail.service';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { S3Service } from 'src/providers/s3/s3.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { GroupsService } from '../groups/groups.service';
import { LLMModelsService } from '../llm-models/llm-models.service';
import { UsersService } from '../users/users.service';
import { EmailConfig } from './summary.dto';
import {
  ActiveMember,
  AdminSummaryData,
  CALENDAR_INTERVAL,
  DashboardData,
  DashboardKey,
  DepartmentBoardData,
  DepartmentBoardSortType,
  GroupFilter,
  LeaderboardData,
  LeaderboardSoryType,
  LeaderboardSummaryQueryData,
  LeaderboardType,
  SearchType,
  SortDataType,
  SummaryData,
  SummaryLLmList,
  TotalGroupInfo,
} from './summary.interface';
import {
  botSummaryKey,
  formatLeaderboardSummaryData,
  formatQueryResponseToDashboardData,
  formatQueryResponseToMap,
} from './summary.utils';
import { UserRequest } from '../auth/auth.interface';
import { FileHistoryService } from '../file-history/file-history.service';
import { group } from 'console';
import { LabelsService } from '../labels/labels.service';
import { GroupsQueueDto } from '../groups/groups.dto';
import { Readable } from 'stream';
@Injectable()
export class SummaryService {
  constructor(
    private prisma: PrismaService,
    private s3Service: S3Service,
    private configService: ConfigService,
    private mailService: MailService,
    private featureFlagService: FeatureFlagService,
    private readonly groupsService: GroupsService,
    private readonly usersService: UsersService,
    private readonly elasticSearchService: ElasticSearchService,
    private readonly llmModelService: LLMModelsService,
    private readonly fileHistoryService: FileHistoryService,
    private readonly labelsService: LabelsService,
  ) {}
  private logger = new Logger(SummaryService.name);

  async checkIsEntityIdValid(entityType: FileEntityType, entityId: number): Promise<boolean> {
    let isValid = false;
    switch (entityType) {
      case FileEntityType.BOT:
      case FileEntityType.FLOW:
      case FileEntityType.INSIGHT: {
        isValid = await this.checkIsGroupEntityIdValid(entityId);
        break;
      }
      case FileEntityType.USER: {
        isValid = await this.checkIsUserEntityIdValid(entityId);
        break;
      }
      default: {
        throw new ApiException(ErrorCode.FILE_ENTITY_TYPE_INVALID);
      }
    }
    return isValid;
  }

  private async checkIsUserEntityIdValid(userId: number): Promise<boolean> {
    const user = await this.usersService.getUser(userId);
    if (user) return true;
    return false;
  }

  private async checkIsGroupEntityIdValid(groupId: number): Promise<boolean> {
    const group = await this.groupsService.getGroup(groupId, {});
    if (group) return true;
    return false;
  }

  async sendSummaryReportEmail(
    fileType: FileType,
    s3FilePath: string,
    dateFrom: string,
    dateTo: string,
  ) {
    const bucket = this.configService.get<string>(`s3.reportFilesBuckets`);
    const s3Response= await this.s3Service.get(bucket, s3FilePath);
    const fileRes = s3Response.Body as Readable;

    const backendEnv = this.configService.get<string>('backendEnv');

    const emailConfig = this.getDailyReportEmailMapping(fileType, backendEnv, dateFrom, dateTo);
    if (!emailConfig) {
      this.logger.error(`The file type ${fileType} doesn't support to send email`);
      throw new ApiException(ErrorCode.INVALID_REPORT_TYPE);
    }
    const { isEnabled, metaData }: FeatureFlag = await this.featureFlagService.getOne(
      emailConfig.featureFlagKey,
    );

    const { recipient, cc, bcc } = metaData as Partial<{
      recipient: string;
      cc: string;
      bcc: string;
    }>;

    if (!recipient || !isEnabled) {
      this.logger.log('Sending report feature is not enabled');
      return;
    }

    const res = await this.mailService.send({
      to: recipient,
      cc,
      bcc,
      template: 'summary/daily-report',
      data: {
        title: emailConfig.emailTitle,
        desc: emailConfig.emailDesc,
      },
      attachments: [
        {
          filename: emailConfig.attachmentName,
          content: fileRes,
        },
      ],
    });
    this.logger.log(res, `The result of sending report - ${fileType}`);
    return res;
  }

  async getGroups(queues: GroupsQueueDto) {
    const { skip, take, where, fromDate, toDate, orderBy } = queues;
    try {
      const groups = await this.prisma.group.findMany({
        include: {
          _count: {
            select: {
              memberships: true,
              ModelFiles: true,
              apikeys: true,
            },
          },
          memberships: {
            where: {
              roleId: 1,
            },
            include: {
              user: {
                select: {
                  name: true,
                  id: true,
                },
              },
            },
          },
          createdBy: {
            select: {
              name: true,
              id: true,
            },
          },
          ...(queues.where.groupType == 'BOT'
            ? {
                llmModel: {
                  select: {
                    ...(this.labelsService.getLabelsPrismaQuery('LLM_MODEL') as any),
                    makeLiveToPublic: true,
                    id: true,
                  },
                },
              }
            : {}),
        },
        skip,
        take,
        orderBy,
        where,
      });
      const result = await Promise.all(
        groups.map(async (group) => {
          const rangedUsages = await this.prisma.summary.findMany({
            where: {
              groupId: group.id,
              key: SummaryKeyType.TOTAL_COMPLETION_TOKENS_TOTAL,
              startDate: { gte: new Date(fromDate), lte: addDays(new Date(toDate), 1) },
            },
          });
          const rangedNumOfToken = rangedUsages.reduce((acc, botUsage) => {
            acc += botUsage.value;
            return acc;
          }, 0);

          const overallUsages = await this.prisma.summaryAll.findMany({
            where: {
              groupId: group.id,
              key: SummaryKeyType.TOTAL_COMPLETION_TOKENS_TOTAL,
            },
          });
          const totalNumOfToken = overallUsages.reduce((acc, botUsage) => {
            acc += botUsage.value.toNumber();
            return acc;
          }, 0);

          let lastChatDate = null;
          const apiCallTotalUsage = await this.prisma.summary.findFirst({
            where: {
              groupId: group.id,
              key: SummaryKeyType.CALL_TOTAL,
              startDate: { gte: new Date(fromDate), lte: addDays(new Date(toDate), 1) },
            },
            orderBy: {
              startDate: 'desc',
            },
          });
          if (apiCallTotalUsage) {
            this.logger.log(`date ${apiCallTotalUsage.startDate}`);
            lastChatDate = apiCallTotalUsage.startDate.toISOString();
          }

          const llmModel = await this.prisma.lLMModel.findFirst({
            select: { active: true },
            where: { groupId: group.id },
          });
          return {
            ...(queues.where.groupType == 'BOT'
              ? {
                  ...group,
                  llmModel: this.labelsService.formatLabelsAndCategoriesData(group?.llmModel),
                }
              : group),
            active: llmModel?.active ?? true,
            status: apiCallTotalUsage ? 'ACTIVE' : 'INACTIVE',
            lastChatDate,
            rangedNumOfToken,
            totalNumOfToken,
          };
        }),
      );
      return result;
    } catch (error) {
      this.logger.error(error, 'Failed to get bot list');
      return [];
    }
  }

  async getGroupCount(groupType: GroupType, where?: Prisma.GroupWhereInput) {
    const count = await this.prisma.group.count({
      where: {
        ...where,
        groupType,
      },
    });
    return count;
  }

  private getDailyReportEmailMapping(
    fileType: FileType,
    env: string,
    dateFrom: string,
    dateTo: string,
  ): EmailConfig {
    switch (fileType) {
      case FileType.BOT_SUMMARY:
        return {
          featureFlagKey: FeatureFlagKey.BOT_REPORT_CONFIG,
          attachmentName: `BotBuilder-DailyBotListReport-${dateFrom}-${dateTo}.xlsx`,
          emailTitle: `[${env}] Bot Builder - Daily Bot List Report: ${dateFrom} - ${dateTo}`,
          emailDesc: `Please find attached the Bot builder [${env}] bot list report in excel format. Thank you.`,
        };
      case FileType.USER_SUMMARY:
        return {
          featureFlagKey: FeatureFlagKey.USER_REPORT_CONFIG,
          attachmentName: `BotBuilder-DailyUserListReport-${dateFrom}-${dateTo}.xlsx`,
          emailTitle: `[${env}] Bot Builder - Daily User List Report: ${dateFrom} - ${dateTo}`,
          emailDesc: `Please find attached the Bot builder [${env}] user list report in excel format. Thank you.`,
        };
      default:
        return null;
    }
  }

  async fetchDashboardDataByDashboardKeyMap(
    groupId: number,
    calendarInterval: CALENDAR_INTERVAL,
    fromDate: Date,
    toDate: Date,
  ): Promise<Map<DashboardKey, DashboardData[]>> {
    const group = await this.groupsService.getGroup(groupId, {});
    let searchType = SearchType.NORMAL;
    if (group.groupType === GroupType.FLOW) {
      searchType = SearchType.FLOW_DETAIL;
    }
    try {
      const dashboardData = await this.fetchSummaryBotLineChartData(
        calendarInterval,
        fromDate,
        toDate,
        {
          groupId,
          searchType,
        },
      );
      const newRegisterMemberInfo = await this.getNewRegisterMemberLineChartData(
        calendarInterval,
        fromDate,
        toDate,
        { id: groupId },
      );
      dashboardData.set('MEMBERS', newRegisterMemberInfo);
      if (group.groupType === GroupType.FLOW) {
        await this.fetchSummaryFlowLineChartData(
          calendarInterval,
          fromDate,
          toDate,
          dashboardData,
          {
            id: groupId,
          },
        );
      }
      return dashboardData;
    } catch (err) {
      this.logger.error(err, 'fetch dashboard data failed');
      throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  async fetchAdminDashboardDataByDashboardKeyMap(
    calendarInterval: CALENDAR_INTERVAL,
    fromDate: Date,
    toDate: Date,
    groupType?: GroupType,
    env?: Environment,
  ): Promise<Map<DashboardKey, DashboardData[]>> {
    const dashboardData = (await this.fetchSummaryBotLineChartData(
      calendarInterval,
      fromDate,
      toDate,
      {
        groupType,
        env,
      },
    )) as Map<DashboardKey, DashboardData[]>;
    const newRegisterUserInfo = (await this.prisma
      .$queryRaw`select date_trunc(${calendarInterval}, "createdAt") as label , count(id) as value from "User" 
      where  "createdAt" >= ${fromDate} and "createdAt" <= ${toDate}
      group by label
    `) as DashboardData[];
    const newRegisterUsers = formatQueryResponseToDashboardData(newRegisterUserInfo, 'MEMBERS');
    dashboardData.set('MEMBERS', newRegisterUsers);
    await this.fetchSummaryFlowLineChartData(calendarInterval, fromDate, toDate, dashboardData, {
      env,
    });
    const noOfBotCreated = (await this.prisma
      .$queryRaw`SELECT date_trunc(${calendarInterval}, g."createdAt")::date as label, coalesce(count(g.name), 0) as value
    FROM "Group" as g  where g."groupType" = 'BOT' and g."env" = 'TEST' and   g."createdAt" >= ${fromDate} and g."createdAt" <= ${toDate}
    group by label`) as DashboardData[];
    const numberOfBotCreated = formatQueryResponseToDashboardData(noOfBotCreated, 'BOT_CREATE');
    dashboardData.set('BOT_CREATE', numberOfBotCreated);

    return dashboardData;
  }
  async getDashboardEngineSlug(groupId?: number): Promise<Map<SummaryKeyType, string[]>> {
    let searchType = SearchType.NORMAL;
    let where: Prisma.SummaryWhereInput = {};
    const engineSlugMap = new Map<SummaryKeyType, string[]>();
    if (groupId) {
      const group = await this.groupsService.getGroup(groupId, {});
      if (group.groupType === GroupType.FLOW) {
        searchType = SearchType.FLOW_DETAIL;
      }
      where = { groupId };
      if (searchType === SearchType.FLOW_DETAIL) {
        where = {
          flowId: groupId,
        };
      }
    }

    const engineSlugList = await this.prisma.summary
      .groupBy({
        by: ['engineSlug', 'key'],
        where,
      })
      .catch((err) => {
        this.logger.error(err, 'fetch dashboard EngineSlug failed');
        throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
      });
    if (engineSlugList?.length > 0) {
      engineSlugList
        .filter((item) => item.engineSlug.trim().length > 0)
        .forEach((item) => {
          const summaryKey = item.key;
          if (engineSlugMap.has(summaryKey)) {
            engineSlugMap.get(summaryKey).push(item.engineSlug);
          } else {
            engineSlugMap.set(summaryKey, [item.engineSlug]);
          }
        });
    }
    return engineSlugMap;
  }

  async fetchLeaderboardByType(
    leaderboardType: LeaderboardType,
    fetchDataType: SortDataType,
    sort: LeaderboardSoryType,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<LeaderboardData[]> {
    if (sort === 'FILE_STORAGE') {
      const columnInfo = [LeaderboardType.ACTIVE_USER, LeaderboardType.USER].includes(
        leaderboardType,
      )
        ? Prisma.sql`"uploaderId"`
        : Prisma.sql`"groupId"`;
      const uploadedFilesSummary = (await this.prisma.$queryRaw`
        select count(m.id) as _count,${
          leaderboardType === LeaderboardType.GROUP
            ? Prisma.sql` g.name `
            : Prisma.sql`case when e.email is not null then e.email else u.name end`
        } label, m.${columnInfo}  from "ModelFile" m
        ${
          [LeaderboardType.USER].includes(leaderboardType)
            ? Prisma.sql`left join "User" u on u.id = m."uploaderId"
                      left join "Email" e on u."prefersEmailId" = e.id `
            : Prisma.sql`left join "Group" g on m."groupId" = g.id`
        }
        where ${columnInfo} is not null
        ${
          fetchDataType != SortDataType.ACCUMULATED
            ? Prisma.sql`
          and m."createdAt" >= ${fromDate} and  m."createdAt" <=${toDate} `
            : Prisma.empty
        }
        group by m.${columnInfo},label
        order by _count desc
        limit 10
      `) as { _count: number; label: string }[];
      return uploadedFilesSummary.map((item) => ({ ...item, sum: item._count }));
    } else {
      const fetchTable =
        fetchDataType === SortDataType.ACCUMULATED
          ? Prisma.sql`"SummaryAll" `
          : Prisma.sql`"Summary"`;
      const columnInfo = this.getColumnInfoByLeaderboardType(leaderboardType);
      const labelInfoColumn = this.getLeaderboardTypeColumn(leaderboardType);
      const filterSql = this.genLeaderboardFilterSql(
        leaderboardType,
        fetchDataType,
        fromDate,
        toDate,
      );
      const data = (await this.prisma
        .$queryRaw`select s."callingType", s.${columnInfo} as "labelId",  ${
        LeaderboardType.ACTIVE_USER === leaderboardType
          ? Prisma.sql`count(distinct(s."callingBy"))`
          : Prisma.sql`sum(s.value)`
      } as _sum, ${labelInfoColumn} as "labelName" from ${fetchTable} s
        ${
          LeaderboardType.USER === leaderboardType
            ? Prisma.sql`left join "User" u on u.id = s."callingBy"
                      left join "Email" e on u."prefersEmailId" = e.id `
            : Prisma.sql`left join "Group" g on s."groupId" = g.id`
        }
        where ${
          LeaderboardType.ACTIVE_USER === leaderboardType
            ? Prisma.sql`s."callingBy" <> 0 and s."callingType" in ('USER_PLAYGROUND','TEAMS') `
            : Prisma.sql`s.${columnInfo} in (select s2.${columnInfo} from ${fetchTable} s2
            ${
              LeaderboardType.TOP_TEAMS_BOT === leaderboardType
                ? Prisma.sql`left join "LLMModel" llm on s2."groupId" = llm."groupId"`
                : Prisma.empty
            }
              where cast(s2.key as text) = ${sort}
              ${filterSql}
              group by s2.${columnInfo}
              order by sum(s2.value) desc
              limit 10
            ) and cast(s.key as text) = ${sort}`
        }
      ${
        fetchDataType !== SortDataType.ACCUMULATED
          ? Prisma.sql` and s."startDate" >= ${fromDate} and s."startDate" <= ${toDate}`
          : Prisma.empty
      }
      group by s."callingType", s.${columnInfo}, "labelName"
      order by _sum desc
      ${LeaderboardType.ACTIVE_USER === leaderboardType ? Prisma.sql`limit 10` : Prisma.empty}
      `) as LeaderboardSummaryQueryData[];
      if (LeaderboardType.ACTIVE_USER === leaderboardType) {
        return data.map((item) => ({ ...item, sum: item._sum, label: item.labelName }));
      }
      const result = formatLeaderboardSummaryData(data);
      return result;
    }
  }

  private getLeaderboardTypeColumn(type: LeaderboardType) {
    switch (type) {
      case LeaderboardType.USER:
        return Prisma.sql` case when e.email is not null then e.email else u.name end `;
      case LeaderboardType.GROUP:
        return Prisma.sql`concat(g."name",'^_^',g."groupType")`;
      case LeaderboardType.ACTIVE_USER:
        return Prisma.sql`concat(g."name",'^_^',g."groupType")`;
      case LeaderboardType.TOP_TEAMS_BOT:
        return Prisma.sql`concat(g."name",'^_^','Teams BOT')`;
      default:
        return Prisma.sql`s."engineSlug"`;
    }
  }

  private genLeaderboardFilterSql(
    type: LeaderboardType,
    fetchDataType: SortDataType,
    fromDate?: Date,
    toDate?: Date,
  ) {
    let filterDataSql;

    switch (type) {
      case LeaderboardType.LLM:
        filterDataSql = Prisma.sql`and "engineSlug" <> '' `;
        break;
      case LeaderboardType.USER:
        filterDataSql = Prisma.sql`and "callingBy" <> 0 and "callingType" in ('USER_PLAYGROUND','TEAMS')`;
        break;
      case LeaderboardType.ACTIVE_USER:
        filterDataSql = Prisma.sql`and "callingBy" <> 0 and "callingType" in ('USER_PLAYGROUND','TEAMS')`;
        break;
      case LeaderboardType.TOP_TEAMS_BOT:
        filterDataSql = Prisma.sql`and llm."showInTeams" = true`;
        break;
      default:
        filterDataSql = Prisma.empty;
        break;
    }
    if (fetchDataType != SortDataType.ACCUMULATED) {
      return Prisma.sql`${filterDataSql} ${Prisma.sql` and s2."startDate" >= ${fromDate} and s2."startDate" <= ${toDate}`}`;
    } else {
      return filterDataSql;
    }
  }

  private getColumnInfoByLeaderboardType(type: LeaderboardType) {
    if (
      [LeaderboardType.ACTIVE_USER, LeaderboardType.GROUP, LeaderboardType.TOP_TEAMS_BOT].includes(
        type,
      )
    ) {
      return Prisma.sql`"groupId"`;
    } else if ([LeaderboardType.USER].includes(type)) {
      return Prisma.sql`"callingBy"`;
    } else {
      return Prisma.sql`"engineSlug"`;
    }
  }

  async fetchActiveMemberList(
    groupId: number,
    fromDate: Date,
    toDate: Date,
  ): Promise<ActiveMember[]> {
    const activeMembers: ActiveMember[] = [];
    const userReqData = await this.prisma.summary.groupBy({
      by: ['callingBy', 'key'],
      where: {
        groupId,
        startDate: {
          lte: toDate,
          gte: fromDate,
        },
        callingBy: {
          not: 0,
        },
        callingType: { in: [SummaryCallingType.TEAMS, SummaryCallingType.USER_PLAYGROUND] },
      },
      _sum: { value: true },
    });
    if (userReqData.length === 0) {
      return [];
    }
    const userIds = new Set(userReqData.map((item) => item.callingBy));
    const activeMemberInfo = await this.prisma.membership.findMany({
      include: {
        Role: {
          select: {
            name: true,
          },
        },
        user: {
          select: {
            name: true,
            lastLoginDate: true,
            emails: true,
          },
        },
      },
      where: {
        groupId,
        userId: { in: Array.from(userIds) },
      },
    });
    activeMemberInfo.forEach((memberInfo) => {
      const activeMember: ActiveMember = {};
      activeMember.userId = memberInfo.userId;
      activeMember.userName = memberInfo.user.name;
      activeMember.lastLoginAt = memberInfo.user.lastLoginDate;
      activeMember.email = memberInfo.user.emails[0] ? memberInfo.user.emails[0]?.email : ' ';
      activeMember.role = memberInfo.Role.name;
      botSummaryKey.forEach((summaryKey) => {
        activeMember[summaryKey] = userReqData.find(
          (item) => item.key === summaryKey && item.callingBy === activeMember.userId,
        )?._sum?.value;
      });
      activeMembers.push(activeMember);
    });
    return activeMembers;
  }

  async getGroupSummaryData(groupId: number, fromDate: Date, toDate: Date): Promise<SummaryData> {
    const group = await this.groupsService.getGroup(groupId, {});
    const activeMemberNum = await this.getActiveMemberNum(
      fromDate,
      toDate,
      group.groupType,
      group.env,
      groupId,
    );
    const totalMember = await this.prisma.membership.count({ where: { groupId } });
    const groupUploadFileInfo = await this.prisma.modelFile.aggregate({
      where: { groupId, deletedAt: null },
      _count: {
        groupId: true,
      },
      _sum: {
        fileSize: true,
      },
    });
    let totalFlowConnected = 0;
    let totalBotConnected = 0;
    if (group.groupType === GroupType.BOT) {
      totalFlowConnected = await this.prisma.flowBot.count({
        where: { botGroupId: groupId, status: FlowBotStatus.ACTIVE },
      });
    } else {
      totalBotConnected = await this.prisma.flowBot.count({
        where: { flowGroupId: groupId, status: FlowBotStatus.ACTIVE },
      });
    }
    const timeRangeChartData = await this.prisma.summary.groupBy({
      by: ['key', 'groupId'],
      where: {
        groupId,
        startDate: {
          gte: fromDate,
          lte: toDate,
        },
      },
      _sum: {
        value: true,
      },
    });
    const currentTotalChartData = await this.prisma.summaryAll.groupBy({
      by: ['key', 'groupId'],
      where: {
        groupId,
      },
      _sum: {
        value: true,
      },
    });
    const currentTotalChartMap = currentTotalChartData.reduce((result, item) => {
      result[item.key] = item._sum.value ?? 0;
      return result;
    }, {});
    const timeRangeChartDataMap = timeRangeChartData.reduce((result, item) => {
      result[item.key] = item._sum.value ?? 0;
      return result;
    }, {});

    const currentUsedMonthlyToken = await this.prisma.summary.aggregate({
      where: {
        groupId,
        key: {
          in: [SummaryKeyType.TOTAL_COMPLETION_TOKENS_TOTAL, SummaryKeyType.EMBEDDING_TOKENS_TOTAL],
        },
        startDate: {
          gte: moment().startOf('month').toISOString(),
          lte: moment().endOf('month').toISOString(),
        },
      },
      _sum: {
        value: true,
      },
    });

    const teamsUserTotal = await this.prisma.summaryAll.groupBy({
      by: ['callingAttributes', 'callingBy'],
      where: {
        groupId,
        callingType: SummaryCallingType.TEAMS,
        key: SummaryKeyType.CALL_TOTAL,
      },
    });
    const teamsActiveUser = await this.prisma.summary.groupBy({
      by: ['callingAttributes', 'callingBy'],
      where: {
        groupId,
        callingType: SummaryCallingType.TEAMS,
        key: SummaryKeyType.CALL_TOTAL,
        startDate: {
          gte: fromDate,
          lte: toDate,
        },
      },
    });

    const summaryData: SummaryData = {
      activeMemberNum,
      totalMember,
      totalBotConnected,
      totalFlowConnected,
      totalUploadFile: groupUploadFileInfo._count?.groupId,
      totalDataStorage: groupUploadFileInfo._sum?.fileSize,
      totalApiCalls: currentTotalChartMap[SummaryKeyType.CALL_TOTAL],
      totalTotalCompletion: currentTotalChartMap[SummaryKeyType.TOTAL_COMPLETION_TOKENS_TOTAL],
      currentTotalCompletion: currentTotalChartMap[SummaryKeyType.COMPLETION_TOKENS_TOTAL],
      totalPrompt: currentTotalChartMap[SummaryKeyType.PROMPT_TOKENS_TOTAL],
      totalEmbedding: currentTotalChartMap[SummaryKeyType.EMBEDDING_TOKENS_TOTAL],
      apiCalls: timeRangeChartDataMap[SummaryKeyType.CALL_TOTAL],
      totalCompletion: timeRangeChartDataMap[SummaryKeyType.TOTAL_COMPLETION_TOKENS_TOTAL],
      prompt: timeRangeChartDataMap[SummaryKeyType.PROMPT_TOKENS_TOTAL],
      completion: timeRangeChartDataMap[SummaryKeyType.COMPLETION_TOKENS_TOTAL],
      embedding: timeRangeChartDataMap[SummaryKeyType.EMBEDDING_TOKENS_TOTAL],
      currentUsedMonthlyToken: currentUsedMonthlyToken._sum.value || 0,
      teamsUserTotal: teamsUserTotal?.length ?? 0,
      teamsActiveUser: teamsActiveUser?.length ?? 0,
    };
    return summaryData;
  }

  async getAdminSummaryData(
    fromDate: Date,
    toDate: Date,
    groupType?: GroupType,
    groupEnv?: Environment,
  ): Promise<AdminSummaryData> {
    const { groupFilter, groupIdsFilterSql } = this.genSummayGroupFilterORMQuery(
      groupType,
      groupEnv,
    );
    const summaryTimeRangeDataQuery: Prisma.SummaryWhereInput = {
      startDate: {
        gte: fromDate,
        lte: toDate,
      },
      group: groupFilter,
    };
    const groupInfoResult = await this.getGroupInfo();
    const totalUser = await this.prisma.user.count();
    const activeUserNum = await this.getActiveMemberNum(fromDate, toDate, groupType, groupEnv);
    const groupUploadFileInfo = await this.prisma.modelFile.aggregate({
      where: {
        group: groupFilter,
        deletedAt: null,
      },
      _count: {
        groupId: true,
      },
      _sum: {
        fileSize: true,
      },
    });
    const timeRangeChatData = await this.prisma.summary.groupBy({
      by: ['key'],
      where: {
        ...summaryTimeRangeDataQuery,
        ...groupIdsFilterSql,
        group: groupFilter,
      },
      _sum: {
        value: true,
      },
    });
    const currentTotalChatData = await this.prisma.summary.groupBy({
      by: ['key'],
      where: {
        ...groupIdsFilterSql,
        group: groupFilter,
      },
      _sum: {
        value: true,
      },
    });
    const timeRangeflowCall = await this.prisma.summary.aggregate({
      where: {
        key: SummaryKeyType.CALL_TOTAL,
        ...summaryTimeRangeDataQuery,
        groupId: {
          equals: this.prisma.summary.fields.flowId,
        },
      },
      _sum: {
        value: true,
      },
    });
    const flowCallTotal = await this.prisma.summary.aggregate({
      where: {
        key: SummaryKeyType.CALL_TOTAL,
        groupId: {
          equals: this.prisma.summary.fields.flowId,
        },
        group: {
          env: groupEnv,
        },
      },
      _sum: {
        value: true,
      },
    });
    const currentTotalChartMap = currentTotalChatData.reduce((result, item) => {
      result[item.key] = item._sum.value ?? 0;
      return result;
    }, {});
    const timeRangeChartDataMap = timeRangeChatData.reduce((result, item) => {
      result[item.key] = item._sum.value ?? 0;
      return result;
    }, {});
    const activeGroupInfo = await this.getActiveGroupInfo(fromDate, toDate, groupEnv);
    const teamsUserTotal = await this.prisma.summaryAll.groupBy({
      by: ['callingAttributes', 'callingBy'],
      where: {
        callingType: SummaryCallingType.TEAMS,
        key: SummaryKeyType.CALL_TOTAL,
      },
    });
    const teamsActiveUser = await this.prisma.summary.groupBy({
      by: ['callingAttributes', 'callingBy'],
      where: {
        callingType: SummaryCallingType.TEAMS,
        key: SummaryKeyType.CALL_TOTAL,
        startDate: {
          gte: fromDate,
          lte: toDate,
        },
      },
    });
    const summaryData: AdminSummaryData = {
      activeUserNum,
      totalGroupInfo: groupInfoResult,
      activeBotNum:
        activeGroupInfo.find((item) => item.groupType === GroupType.BOT)?._count.groupType || 0,
      activeFlowNum:
        activeGroupInfo.find((item) => item.groupType === GroupType.FLOW)?._count.groupType || 0,
      totalUser: totalUser || 0,
      totalUploadFile: groupUploadFileInfo._count?.groupId,
      totalDataStorage: groupUploadFileInfo._sum?.fileSize,
      flowApiCalls: timeRangeflowCall?._sum?.value || 0,
      totalApiFlowCalls: flowCallTotal?._sum?.value || 0,
      botApiCalls: timeRangeChartDataMap[SummaryKeyType.CALL_TOTAL],
      totalApiBotCalls: currentTotalChartMap[SummaryKeyType.CALL_TOTAL],
      totalTotalCompletion: currentTotalChartMap[SummaryKeyType.TOTAL_COMPLETION_TOKENS_TOTAL],
      currentTotalCompletion: currentTotalChartMap[SummaryKeyType.COMPLETION_TOKENS_TOTAL],
      totalPrompt: currentTotalChartMap[SummaryKeyType.PROMPT_TOKENS_TOTAL],
      totalEmbedding: currentTotalChartMap[SummaryKeyType.EMBEDDING_TOKENS_TOTAL],
      totalCompletion: timeRangeChartDataMap[SummaryKeyType.TOTAL_COMPLETION_TOKENS_TOTAL],
      prompt: timeRangeChartDataMap[SummaryKeyType.PROMPT_TOKENS_TOTAL],
      completion: timeRangeChartDataMap[SummaryKeyType.COMPLETION_TOKENS_TOTAL],
      embedding: timeRangeChartDataMap[SummaryKeyType.EMBEDDING_TOKENS_TOTAL],
      teamsUserTotal: teamsUserTotal?.length ?? 0,
      teamsActiveUser: teamsActiveUser?.length ?? 0,
    };
    return summaryData;
  }

  async getSummaryLLmEngineList(
    fromDate: Date,
    toDate: Date,
    sortDataType: SortDataType,
    orderBy: Record<string, 'asc' | 'desc'>,
    skip?: number,
    take?: number,
    group?: Group,
  ) {
    const dateQuery = {
      startDate: {
        gte: fromDate,
        lte: toDate,
      },
    };
    let queryTable;
    if (sortDataType == SortDataType.ACCUMULATED) {
      queryTable = this.prisma.summaryAll;
    } else {
      queryTable = this.prisma.summary;
    }
    const llmEngineQuery = {
      ...(group
        ? group.groupType == GroupType.FLOW
          ? { flowId: group.id }
          : { groupId: group.id }
        : {}),
      key: Object.keys(orderBy)[0] as SummaryKeyType,
      ...(sortDataType != SortDataType.ACCUMULATED ? dateQuery : {}),
      NOT: {
        engineSlug: '',
      },
    };
    const llmEngines = await queryTable.groupBy({
      by: ['engineSlug'],
      where: llmEngineQuery,
      _sum: {
        value: true,
      },
      orderBy: {
        _sum: {
          value: Object.values(orderBy)[0],
        },
      },
      take,
      skip,
    });
    const count = await queryTable.groupBy({
      by: ['engineSlug'],
      where: llmEngineQuery,
      _sum: {
        value: true,
      },
      orderBy: {
        _sum: {
          value: Object.values(orderBy)[0],
        },
      },
    });
    if (!llmEngines || llmEngines.length == 0) {
      return { count: 0, list: [] };
    }
    const summaryWhere: Prisma.SummaryAllWhereInput = {
      ...(group
        ? group.groupType == GroupType.FLOW
          ? { flowId: group.id }
          : { groupId: group.id }
        : {}),
      key: {
        in: [
          SummaryKeyType.CALL_TOTAL,
          SummaryKeyType.TOTAL_COMPLETION_TOKENS_TOTAL,
          SummaryKeyType.EMBEDDING_TOKENS_TOTAL,
        ],
      },
      engineSlug: {
        in: llmEngines.map((item) => item.engineSlug),
      },
      ...(group ? (group.groupType == GroupType.FLOW ? { NOT: { flowId: group.id } } : {}) : {}),
    };
    const accumulated = await this.prisma.summaryAll.groupBy({
      by: ['engineSlug', 'key'],
      where: summaryWhere,
      _sum: {
        value: true,
      },
    });
    const timeRange = await this.prisma.summary.groupBy({
      by: ['engineSlug', 'key'],
      where: { ...(summaryWhere as Prisma.SummaryWhereInput), ...dateQuery },
      _sum: {
        value: true,
      },
    });
    const llmEngineList: SummaryLLmList[] = llmEngines.map((item) => {
      const timeRangeData = timeRange.filter(
        (timeRangeItem) => timeRangeItem.engineSlug === item.engineSlug,
      );
      const accumulatedData = accumulated.filter(
        (accumulatedItem) => accumulatedItem.engineSlug === item.engineSlug,
      );
      return {
        llmEngineName: item.engineSlug,
        timeRangeChatNum:
          timeRangeData?.find((timeRangeItem) => timeRangeItem.key === 'CALL_TOTAL')?._sum?.value ||
          0,
        accumulatedChatNum:
          accumulatedData?.find((timeRangeItem) => timeRangeItem.key === 'CALL_TOTAL')?._sum
            ?.value || 0,
        timeRangeTokenNum:
          timeRangeData?.find(
            (timeRangeItem) => timeRangeItem.key === 'TOTAL_COMPLETION_TOKENS_TOTAL',
          )?._sum?.value || 0,
        accumulatedTokenNum:
          accumulatedData?.find(
            (timeRangeItem) => timeRangeItem.key === 'TOTAL_COMPLETION_TOKENS_TOTAL',
          )?._sum?.value || 0,
        timeRangeEmbeddingNum:
          timeRangeData?.find((timeRangeItem) => timeRangeItem.key === 'EMBEDDING_TOKENS_TOTAL')
            ?._sum?.value || 0,
        accumulatedEmbeddingNum:
          accumulatedData?.find((timeRangeItem) => timeRangeItem.key === 'EMBEDDING_TOKENS_TOTAL')
            ?._sum?.value || 0,
      };
    });
    return { count: count.length, list: llmEngineList };
  }
  private async getGroupInfo(): Promise<TotalGroupInfo[]> {
    const groupInfo = await this.prisma.group.groupBy({
      by: ['env', 'groupType'],
      where: {
        isDeprecated: false,
      },
      _count: {
        env: true,
      },
    });
    let groupInfoResult: TotalGroupInfo[] = [];
    const disableBotsTest = await this.prisma.lLMModel.count({
      where: {
        active: false,
        group: {
          groupType: GroupType.BOT,
          isDeprecated: false,
          env: Environment.TEST,
        },
      },
    });
    const disableBotsProd = await this.prisma.lLMModel.count({
      where: {
        active: false,
        group: {
          groupType: GroupType.BOT,
          isDeprecated: false,
          env: Environment.PROD,
        },
      },
    });
    Object.values(GroupType).forEach((groupType) => {
      if (groupType == GroupType.BOT) {
        groupInfoResult = [
          ...groupInfoResult,
          ...groupInfo
            .filter((item) => item.groupType === groupType)
            .map((item) => ({
              env: item.env,
              groupType,
              isActive: true,
              value:
                item._count.env -
                (item.env === Environment.TEST ? disableBotsTest : disableBotsProd),
            })),
          ...[Environment.TEST, Environment.PROD].map((env) => ({
            env,
            groupType,
            isActive: false,
            value: env === Environment.TEST ? disableBotsTest : disableBotsProd,
          })),
        ];
      } else {
        groupInfoResult = [
          ...groupInfoResult,
          ...groupInfo
            .filter((item) => item.groupType === groupType)
            .map((item) => ({ env: item.env, isActive: true, value: item._count.env, groupType })),
          ...Object.values(Environment).map((env) => ({
            env,
            isActive: false,
            value: 0,
            groupType,
          })),
        ];
      }
    });

    return groupInfoResult;
  }

  private async getActiveGroupInfo(fromDate: Date, toDate: Date, groupEnv?: Environment) {
    const activeGroupList = await this.prisma.group.groupBy({
      by: ['groupType'],
      where: {
        env: groupEnv,
        Summary: {
          some: {
            startDate: {
              lte: toDate,
              gte: fromDate,
            },
          },
        },
      },
      _count: {
        groupType: true,
      },
    });
    return activeGroupList;
  }

  private genSummayGroupFilterORMQuery(groupType?: GroupType, groupEnv?: Environment) {
    const groupFilter: Prisma.GroupWhereInput = {
      env: groupEnv,
      groupType,
    };
    if (groupType && groupType.trim().length > 0) {
      groupFilter.groupType = groupType;
    }

    const groupIdsFilterSql: Prisma.SummaryWhereInput = {
      groupId: {
        not: {
          equals: this.prisma.summary.fields.flowId,
        },
      },
    };
    if (groupType) {
      if (groupType != GroupType.FLOW) {
        groupIdsFilterSql.flowId = 0;
      } else {
        groupIdsFilterSql.flowId = { not: 0 };
      }
    }
    return { groupFilter, groupIdsFilterSql };
  }
  /**
   * @description fill the flow request Line Chart data into dashboardData
   * @param calendarInterval
   * @param fromDate
   * @param toDate
   * @param dashboardData
   * @param groupFilter if put the id please don't put the env
   * @returns
   */
  private async fetchSummaryFlowLineChartData(
    calendarInterval: CALENDAR_INTERVAL,
    fromDate: Date,
    toDate: Date,
    dashboardData: Map<DashboardKey, DashboardData[]>,
    groupFilter: { id?: number; env?: Environment },
  ): Promise<Map<DashboardKey, DashboardData[]>> {
    // when https://github.com/prisma/prisma/issues/6653 supported need change the sql;
    const flowsData = (await this.prisma
      .$queryRaw`select date_trunc(${calendarInterval}, sm."startDate")::date as label, coalesce(sum(sm.value), 0) as value, sm."callingType"
          , sm.key, sm."engineSlug", sm."flowId"
                 from "Summary" as sm
                 left join "Group" g on g.id = sm."groupId"
                 where "startDate" >= ${fromDate} and "startDate" <= ${toDate} 
                 and sm."flowId" = sm."groupId"
                 ${
                   groupFilter.id ? Prisma.sql` and sm."groupId" = ${groupFilter.id}` : Prisma.empty
                 }
                 ${
                   groupFilter.env
                     ? Prisma.sql`and cast(g.env as text) = ${groupFilter.env}`
                     : Prisma.empty
                 }
                 and sm."key" in ('CALL_TOTAL','TOOLS_USAGE_TOTAL')
                 group by label, sm.key, sm."engineSlug", sm."flowId", sm."callingType"
                 order by label`) as DashboardData[];
    const flowsDashboardData: Map<DashboardKey, DashboardData[]> = formatQueryResponseToMap(
      flowsData,
      GroupType.FLOW,
    );
    dashboardData.set('FLOW_CALLS_TOTAL', flowsDashboardData.get('CALL_TOTAL'));
    dashboardData.set('TOOLS_USAGE_TOTAL', flowsDashboardData.get('TOOLS_USAGE_TOTAL'));
    return dashboardData;
  }

  /**
   * @description Get bot summary data into LineChart
   * @param {CALENDAR_INTERVAL} calendarInterval
   * @param {Date} fromDate
   * @param {Date} toDate
   * @param groupFilter  if fill the goupId don't fill the env and groupType the SearchType just can apply when you fill the groupId
   * @returns {Map<DashboardKey, DashboardData[]>} dashboardData
   */
  private async fetchSummaryBotLineChartData(
    calendarInterval: CALENDAR_INTERVAL,
    fromDate: Date,
    toDate: Date,
    groupFilter: GroupFilter,
  ): Promise<Map<DashboardKey, DashboardData[]>> {
    // when https://github.com/prisma/prisma/issues/6653 supported need change the sql;
    const dashboardQueryData = (await this.prisma
      .$queryRaw`select date_trunc(${calendarInterval}, sm."startDate")::date as label, coalesce(sum(sm.value), 0) as value, sm."callingType"
        , sm.key, sm."engineSlug"
               from "Summary" as sm
               left join "Group" g on sm."groupId" = g."id"
               where "startDate" >= ${fromDate} and "startDate"<= ${toDate} 
               and sm."groupId" <> sm."flowId"
               ${
                 groupFilter.groupId
                   ? groupFilter.searchType === SearchType.FLOW_DETAIL
                     ? Prisma.sql` and sm."flowId" = ${groupFilter.groupId}`
                     : Prisma.sql` and sm."groupId" = ${groupFilter.groupId}`
                   : Prisma.empty
               }
               ${
                 groupFilter.groupType
                   ? groupFilter.groupType === GroupType.FLOW
                     ? Prisma.sql` and sm."flowId" <> 0 `
                     : Prisma.sql` and sm."flowId" = 0 AND cast(g."groupType" as text) = ${groupFilter.groupType}`
                   : Prisma.empty
               }
               ${
                 groupFilter.env
                   ? Prisma.sql` and cast(g.env as text) = ${groupFilter.env}`
                   : Prisma.empty
               }
               group by label, sm.key, sm."engineSlug", sm."callingType"
               order by label`) as DashboardData[];
    const dashboardData: Map<DashboardKey, DashboardData[]> = formatQueryResponseToMap(
      dashboardQueryData,
      GroupType.BOT,
    );
    return dashboardData;
  }

  /**
   * @description get the New Register Member data in line Chart data
   * @param calendarInterval
   * @param {CALENDAR_INTERVAL} calendarInterval
   * @param {Date} fromDate
   * @param {Date} toDate
   * @param groupFilter  if fill the id don't fill the env and groupType.
   * @returns
   */
  private async getNewRegisterMemberLineChartData(
    calendarInterval: CALENDAR_INTERVAL,
    fromDate: Date,
    toDate: Date,
    groupFilter: { id?: number; env?: Environment; groupType?: GroupType },
  ): Promise<DashboardData[]> {
    // when https://github.com/prisma/prisma/issues/6653 supported need change the sql;
    const newRegisterMemberInfo = (await this.prisma
      .$queryRaw`select date_trunc(${calendarInterval}, m."createdAt") as label,
     count(m."createdAt") as value from "Membership" m
     left join "Group" g on g.id = m."groupId"
     where m."createdAt" >= ${fromDate}  and m."createdAt" <= ${toDate}
     ${groupFilter.id ? Prisma.sql` and m."groupId" = ${groupFilter.id}` : Prisma.empty}
     ${groupFilter.env ? Prisma.sql` and cast(g.env as text) = ${groupFilter.env}` : Prisma.empty}
     ${
       groupFilter.groupType
         ? Prisma.sql` and cast(g."groupType" as text) = ${groupFilter.groupType}`
         : Prisma.empty
     }
    group by label`) as DashboardData[];
    return formatQueryResponseToDashboardData(newRegisterMemberInfo, 'MEMBERS');
  }

  private async getActiveMemberNum(
    fromDate: Date,
    toDate: Date,
    groupType?: GroupType,
    groupEnv?: Environment,
    groupId?: number,
  ) {
    const activeMemberIds = await this.prisma.summary.groupBy({
      by: ['callingBy'],
      where: {
        callingBy: {
          not: 0,
        },
        callingType: {
          in: [SummaryCallingType.USER_PLAYGROUND],
        },
        startDate: {
          gte: fromDate,
          lte: toDate,
        },
        group: {
          id: groupId,
          env: groupEnv,
          groupType,
        },
      },
    });
    if (!groupId) {
      return activeMemberIds?.length ?? 0;
    }
    const userIds = activeMemberIds.map((item) => item.callingBy);
    const activeMember = await this.prisma.membership.groupBy({
      by: ['userId'],
      where: {
        group: {
          id: groupId,
          env: groupEnv,
          groupType,
        },
        userId: {
          in: userIds,
        },
      },
      _count: {
        userId: true,
      },
    });
    return activeMember.length;
  }

  public async getDepartmentBoardData(
    sortType: DepartmentBoardSortType,
    fromDate: Date,
    toDate: Date,
  ) {
    if (sortType === DepartmentBoardSortType.ACTIVE_GROUP) {
      const data = await this.prisma.group.groupBy({
        by: ['businessUnit'],
        where: {
          businessUnit: {
            not: null,
          },
          Summary: {
            some: {
              startDate: {
                lte: toDate,
                gte: fromDate,
              },
            },
          },
        },
        _count: {
          id: true,
        },
      });
      const _data = data.map(
        (item) => (({ label: item.businessUnit, sum: item._count.id, }) as DepartmentBoardData),
      );
      return _data.sort((a, b) => b.sum - a.sum);
    }
    const activeMemberData: DepartmentBoardData[] = await this.prisma.$queryRaw`
      SELECT g."businessUnit" as "label",count(distinct(x."callingBy")) as "sum" FROM public."Summary" x 
      left join "Group" g  on g.id  = x."groupId" 
      where "callingType" in ('USER_PLAYGROUND','TEAMS')
      AND  g."businessUnit" is not null
      AND  "startDate" >= ${fromDate} and "startDate"<= ${toDate} 
      group by g."businessUnit"
      order by "sum" desc`;
    return activeMemberData;
  }
  /**
   * @description  get same report in the file history table if the report is generated and the s3 file is existed return the file history .
   * if the s3 file is not existed update the file history status to expired
   * @param req
   * @param fromDate
   * @param toDate
   * @param fileType
   * @param entityType
   * @param entityId
   * @returns {FileHistory | undefined}
   */
  public async checkIsGeneratedReport(
    req: UserRequest,
    fromDate: Date,
    toDate: Date,
    fileType: FileType,
    entityType?: FileEntityType,
    entityId?: number,
  ): Promise<FileHistory | undefined> {
    const isEnabledCheckCache = await this.featureFlagService.getOne(
      FeatureFlagKey.ENABLE_SUMMARY_REPORT_CACHE_CHECK,
    );
    if (
      !isEnabledCheckCache ||
      !(isEnabledCheckCache?.isEnabled ?? true) ||
      ['MEMBERSHIP_SUMMARY', 'USER_SUMMARY', 'SUMMARY'].includes(fileType)
    ) {
      return undefined;
    }
    const fileHistory = await this.fileHistoryService.findCompletedByGeneratedParam(
      fromDate,
      toDate,
      fileType,
      entityType,
      entityId,
    );
    if (fileHistory) {
      const bucket = this.configService.get<string>(`s3.reportFilesBuckets`);
      const isExisted = await this.s3Service.isExistedObject(bucket, fileHistory.s3FilePath);
      if (isExisted) {
        this.logger.log(
          `user ${
            req?.user?.id ?? 0
          } } download the report fileType:${fileType} fileType:${fileType} fromDate:${fromDate}`,
        );
        return fileHistory;
      }
      await this.fileHistoryService.updateFileHistory(fileHistory.fileId, {
        status: FileHistoryStatus.EXPIRED,
      });
    }
    return undefined;
  }
}
