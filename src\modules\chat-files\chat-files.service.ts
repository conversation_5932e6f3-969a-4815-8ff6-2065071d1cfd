import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ChatFile,
  ChatFileType,
  FileVerifyStatus,
  FileClassification,
  Group,
  Prisma,
  ScanMalwareStatus,
} from '@prisma/client';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { S3Service } from 'src/providers/s3/s3.service';
import { v4 } from 'uuid';
import { ChatFilesApproach, ChatFilesConfigResponse, ChatFilesResponse } from './chat-files.dto';
import { SecretHashService } from 'src/providers/secret-hash/secret-hash.service';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { FortiSanboxService } from '../../providers/fortisandbox/fortisandbox.service';
import { LLMModelsService } from '../llm-models/llm-models.service';
import {
  DataSourceFileInfo,
  DataSourceFileMovedMapping,
} from '../chat-sessions/dto/update-data-source-request.dto';
import { Readable } from 'stream';
import { LLMBackendService } from '../../providers/llm-backend/llm-backend.service';
import { RedisService } from 'src/providers/redis/redis.service';

@Injectable()
export class ChatFilesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly s3Service: S3Service,
    private readonly secretHashService: SecretHashService,
    private readonly featureFlagService: FeatureFlagService,
    private readonly fortiSanboxService: FortiSanboxService,
    @Inject(forwardRef(() => LLMModelsService))
    private readonly llmModelsService: LLMModelsService,
    @Inject(forwardRef(() => LLMBackendService))
    private llmBackendService: LLMBackendService,
    private redis: RedisService,
  ) {}

  private logger = new Logger(ChatFilesService.name);

  private readonly s3KeyPrefix = {
    [ChatFileType.QUESTION]: 'chat-files',
    [ChatFileType.RESPONSE]: 'chat-files-res',
  };
  private readonly chatFileRetentionDay = {
    [ChatFileType.QUESTION]: 1,
    [ChatFileType.RESPONSE]: 30,
  };
  private readonly validFileExtensions = {
    [ChatFilesApproach.CWD]: ['csv', 'xlsx'],
    [ChatFilesApproach.CWF]: [
      'pdf',
      'csv',
      'jfif',
      'pjpeg',
      'jpeg',
      'pjp',
      'jpg',
      'png',
      'gif',
      'mp3',
      'mp4',
      'wav',
      'txt',
      'xls',
      'xlsx',
    ],
  }; // ove to feature flagm?
  // not require pagination
  async getFiles(
    groupId: number,
    userId: number,
    chatFileType: ChatFileType,
    chatApproach?: ChatFilesApproach,
  ): Promise<ChatFilesResponse> {
    const group = await this.prisma.group.findUnique({ where: { id: groupId } });
    if (!group) {
      throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    }
    // housekeep expired record
    const expiredWhere = {
      groupId,
      userId,
      expiresAt: { lte: new Date() },
    };
    const expiredFiles = await this.prisma.chatFile.findMany({
      where: expiredWhere,
      select: {
        id: true,
        fullScanReportPath: true,
      },
    });
    const expiredReportPaths = expiredFiles
      .map((file) => (file.fullScanReportPath?.trim() !== '' ? file.fullScanReportPath : undefined))
      .filter((path) => path !== undefined && path !== null);
    if (expiredReportPaths.length > 0) {
      await this.llmBackendService.removeS3PIIReportFiles(group.env, expiredReportPaths);
    }
    const expiredFileIds = expiredFiles.map((file) => file.id);
    await this.prisma.chatFile.deleteMany({
      where: {
        id: {
          in: expiredFileIds,
        },
      },
    });
    const where: Prisma.ChatFileWhereInput = {
      groupId,
      userId,
    };
    if (chatFileType) where.type = chatFileType;
    if (chatApproach) {
      where.OR = [
        ...this.validFileExtensions[chatApproach].map((item) => ({
          filename: { endsWith: item },
        })),
      ];
    }
    const chatFiles = await this.prisma.chatFile.findMany({
      where: where,
      orderBy: { id: 'desc' },
    });

    const chatFileList = await this.getChatFileListWithPosition(chatFiles);

    return new ChatFilesResponse(chatFileList);
  }

  private async getChatFileListWithPosition(chatFiles: ChatFile[]): Promise<any[]> {
    const chatFileList = [];
    for (const chatFile of chatFiles) {
      if (chatFile.jobId) {
        const queueName =
          chatFile.fileSize > this.configService.get<number>('splitFileSize', 0)
            ? 'llmguard-scan-large-file'
            : 'llmguard-scan-small-file';
        const position = await this.redis.getJobPositonInQueue(queueName, 'wait', chatFile.jobId);
        chatFileList.push({ ...chatFile, position });
      } else {
        chatFileList.push(chatFile);
      }
    }
    return chatFileList;
  }

  async downloadFile(
    groupId: number,
    userId: number,
    chatFileId: number,
    s3Basename?: string,
    s3Filename?: string,
  ) {
    // supporting 3 types of input to find the file
    let s3BasenameFilter: Prisma.StringFilter | string;
    if (chatFileId) {
      s3BasenameFilter = undefined;
    } else if (s3Basename) {
      s3BasenameFilter = s3Basename;
    } else if (s3Filename) {
      s3BasenameFilter = { startsWith: `${s3Filename}.` };
    }

    const chatFile = await this.prisma.chatFile.findFirst({
      include: { group: true },
      where: {
        id: chatFileId,
        groupId,
        userId,
        s3Basename: s3BasenameFilter,
        expiresAt: { gte: new Date() },
      },
    });
    if (!chatFile) {
      throw new ApiException(ErrorCode.CHAT_FILE_NOT_FOUND);
    }

    return this.s3Service.getObjectUnsafe(
      this.configService.get<string>(`s3.staticFilesBuckets.${chatFile.group.env}`),
      this.getS3FileKey(groupId, userId, chatFile.s3Basename, chatFile.type),
    );
  }

  getS3FileKey(groupId: number, userId: number, s3Basename: string, type: ChatFileType): string {
    const s3KeyPrefixPath = this.s3KeyPrefix[type];
    return `${s3KeyPrefixPath}/${groupId}/${userId}/${s3Basename}`;
  }

  async getPublicUrl(groupId: number, userId: number, s3Basename: string): Promise<string> {
    const fileToken = await this.secretHashService.encodeAES(`${userId}/${s3Basename}`);
    return `${
      process.env['BASE_URL'] ?? 'http://localhost:3001'
    }/v1/groups/${groupId}/chat-files/generated/${fileToken}`;
  }

  async uploadFile(
    groupId: number,
    userId: number,
    file: Express.Multer.File,
    chatApproach: ChatFilesApproach,
    chatFileType: ChatFileType,
    userType: 'user' | 'api-key',
  ): Promise<ChatFile> {
    // validate the file size and number
    await this.validateChatFile(file, userId, groupId, chatApproach, userType);
    const fileExtension = file.originalname.split('.').pop();
    if (!this.validFileExtensions[chatApproach].includes(fileExtension.toLowerCase())) {
      throw new ApiException(ErrorCode.INVALID_FILE_EXTENSION);
    }

    const fileNames = file.originalname.split('.');
    fileNames.pop();
    const s3Basename = `${v4()}.${fileExtension.toLowerCase()}`;
    const s3fileKey = this.getS3FileKey(groupId, userId, s3Basename, chatFileType);

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + this.chatFileRetentionDay[chatFileType]);
    const group = await this.prisma.group.findUnique({ where: { id: groupId } });
    if (!group) {
      throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    }

    const chatFile = await this.prisma.$transaction(async (tx) => {
      const chatFile = await tx.chatFile.create({
        data: {
          groupId,
          userId,
          filename: fileNames.join('.') + '.' + fileExtension.toLowerCase(),
          s3Basename,
          fileSize: file.size,
          expiresAt,
          type: chatFileType,
        },
      });

      await this.s3Service.upload(
        s3fileKey,
        file.originalname,
        file.buffer,
        file.mimetype,
        groupId,
        this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`),
        false,
      );
      return chatFile;
    });
    // malaware scan
    if (chatFile.type === ChatFileType.QUESTION) {
      this.scanMalwareOrPii(chatFile, s3fileKey, group.env, file, true).catch((error: Error) => {
        this.logger.error(`scanMalwareOrPii : ${error.message}`, error.stack);
      });
    }
    return chatFile;
  }

  async deleteFile(groupId: number, userId: number, chatFileId: number): Promise<ChatFile> {
    return this.prisma.$transaction(async (tx) => {
      return await this.deleteChatFile(tx, groupId, userId, chatFileId, true);
    });
  }

  async deleteChatFile(
    tx: Prisma.TransactionClient,
    groupId: number,
    userId: number,
    chatFileId: number,
    isDeletePiiReport?: boolean,
  ) {
    const chatFile = await tx.chatFile.findFirst({
      include: { group: true },
      where: {
        id: chatFileId,
        groupId,
        userId,
        expiresAt: { gte: new Date() },
      },
    });
    if (!chatFile) {
      throw new ApiException(ErrorCode.CHAT_FILE_NOT_FOUND);
    }

    await tx.chatFile.delete({
      where: {
        id: chatFileId,
      },
    });

    await this.s3Service.delete(
      this.configService.get<string>(`s3.staticFilesBuckets.${chatFile.group.env}`),
      this.getS3FileKey(groupId, userId, chatFile.s3Basename, chatFile.type),
    );

    if (
      isDeletePiiReport &&
      chatFile.fullScanReportPath &&
      chatFile.fullScanReportPath.trim() !== ''
    ) {
      await this.llmBackendService.removeS3PIIReportFile(
        chatFile.group.env,
        chatFile.fullScanReportPath,
      );
    }

    delete chatFile.group;
    return chatFile;
  }

  public async uploadInternalChatFile(
    groupId: number,
    userId: number,
    file: Express.Multer.File,
    chatApproach: ChatFilesApproach,
    chatFileType: ChatFileType,
    isPublic: boolean,
  ) {
    if (!isPublic) {
      return await this.uploadFile(groupId, userId, file, chatApproach, chatFileType, 'user');
    }
    const group = await this.prisma.group.findUnique({ where: { id: groupId } });
    if (!group) {
      throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    }
    const fileExtension = file.originalname.split('.').pop();
    const s3Basename = `${v4()}.${fileExtension.toLowerCase()}`;
    const s3fileKey = this.getS3FileKey(groupId, userId, s3Basename, chatFileType);
    await this.s3Service.upload(
      s3fileKey,
      file.originalname,
      file.buffer,
      file.mimetype,
      groupId,
      this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`),
      false,
    );
    return await this.getPublicUrl(groupId, userId, s3Basename);
  }

  async downloadGeneratedPublicFile(groupId: number, chatFileToken: string) {
    const group = await this.prisma.group.findUnique({ where: { id: groupId } });
    if (!group) {
      throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    }
    const decodeToken = await this.secretHashService.decodeAES(chatFileToken);
    if (
      !decodeToken ||
      decodeToken === 'NaN' ||
      decodeToken.trim().length === 0 ||
      decodeToken.indexOf('/') === 0
    ) {
      throw new ApiException(ErrorCode.CHAT_FILE_NOT_FOUND);
    }
    const publicFileInfo = decodeToken.split('/');
    const s3Key = this.getS3FileKey(
      groupId,
      parseInt(publicFileInfo[0]),
      publicFileInfo[1],
      ChatFileType.RESPONSE,
    );

    return this.s3Service.getFileObject(
      this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`),
      s3Key,
    );
  }

  async getChatFileConfig(
    groupId: number,
    chatFileApproach: ChatFilesApproach,
  ): Promise<ChatFilesConfigResponse> {
    const chatWithFileConfig = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      chatFileApproach === ChatFilesApproach.CWF
        ? FeatureFlagKey.BOT_CHAT_WITH_FILE_CONFIG
        : FeatureFlagKey.BOT_CHAT_WITH_DATA_CONFIG,
    );
    if (!chatWithFileConfig) {
      throw new ApiException(ErrorCode.FEATURE_FLAG_NOT_FOUND);
    }
    const maxFileNum = (chatWithFileConfig.metaData?.['maxFileNum'] as number) ?? 0;
    const maxFileSize = (chatWithFileConfig.metaData?.['maxFileSize'] as number) ?? 0;
    const maxSelectionNum = (chatWithFileConfig.metaData?.['maxSelectionNum'] as number) ?? 0;
    return { maxFileNum, maxFileSize, maxSelectionNum };
  }

  private async validateChatFile(
    file: Express.Multer.File,
    userId: number,
    groupId: number,
    chatFileApproach: ChatFilesApproach,
    userType: 'user' | 'api-key',
  ) {
    const chatWithFileConfig = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      chatFileApproach === ChatFilesApproach.CWF
        ? FeatureFlagKey.BOT_CHAT_WITH_FILE_CONFIG
        : FeatureFlagKey.BOT_CHAT_WITH_DATA_CONFIG,
    );

    if (chatWithFileConfig && userType === 'user') {
      const maxFileNum = (chatWithFileConfig.metaData?.['maxFileNum'] as number) ?? 0;
      const maxFileSize = (chatWithFileConfig.metaData?.['maxFileSize'] as number) ?? 0;
      const fileNum = await this.prisma.chatFile.count({ where: { groupId, userId } });
      if (fileNum >= maxFileNum) {
        this.logger.error(
          `chat with file - file number exceeded - userId: ${userId}, groupId - ${groupId}`,
        );
        throw new ApiException(ErrorCode.FILE_NUM_EXCEED);
      }
      if (file.size >= maxFileSize) {
        this.logger.error(
          `chat with file - file size exceeded - userId: ${userId}, groupId - ${groupId}`,
        );
        throw new ApiException(ErrorCode.FILE_TOO_LARGE);
      }
    }
    return true;
  }

  async copyChatFilesToModelFiles(
    tx: Prisma.TransactionClient,
    group: Group,
    userId: number,
    files: DataSourceFileInfo[],
  ) {
    this.logger.log(`copyChatFilesToModelFiles: ${JSON.stringify(files)}`);
    const modelFileDocIds: DataSourceFileMovedMapping[] = [];
    const maxSize = this.configService.get<number>('s3.staticFilesMaxSize');
    const byPassMalwareScan = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.BYPASS_SCAN_MALWARE,
    );
    const chatFiles = await tx.chatFile.findMany({
      where: {
        s3Basename: {
          in: files.map((file) => file.file_id),
        },
        verifyStatus: FileVerifyStatus.VERIFY_SUCCESS,
        scanMalwareStatus: byPassMalwareScan?.isEnabled ? undefined : ScanMalwareStatus.COMPLETED,
        malwareRating: byPassMalwareScan?.isEnabled ? undefined : 'Clean',
        fileSize: { lte: maxSize },
        expiresAt: { gte: new Date() },
      },
    });
    if (chatFiles.length !== files.length) {
      throw new ApiException(ErrorCode.INVALID_CHAT_FILE_TO_EMBEDDING);
    }
    await this.llmModelsService.checkExceedPreUserPreDayUploadLimit(group.id, userId);

    const model = await this.llmModelsService.findOneByGroupId(group.id);
    if (!model) throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);
    if (!this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`))
      throw new InternalServerErrorException('Static file bucket not set');
    const bucket = this.configService.get<string>(`s3.staticFilesBuckets.${group.env}`);
    // copy chat files to model files
    for (const chatFile of chatFiles) {
      const fileUuid = v4();
      const fileKey = `models/${model.modelId}/${fileUuid}.${chatFile.filename.split('.').pop()}`;
      //copy s3 chat file to model file
      await this.s3Service
        .copyFileObject(
          bucket,
          this.getS3FileKey(chatFile.groupId, chatFile.userId, chatFile.s3Basename, chatFile.type),
          bucket,
          fileKey,
        )
        .catch((error) => {
          this.logger.error(error, 'Failed copy s3 object ');
          throw new InternalServerErrorException('Failed copy s3 object');
        });
      //create new model file to db
      const contentType = await this.s3Service.getContentType(bucket, fileKey);
      const updatedDoc = await tx.modelFile.create({
        data: {
          filename: chatFile.filename,
          fileExt: chatFile.filename.split('.').pop().toLowerCase(),
          docId: fileUuid,
          s3Path: fileKey,
          fileSize: chatFile.fileSize,
          fileClassification: FileClassification.PUBLIC_DOMAIN,
          fullScanReportPath: chatFile.fullScanReportPath,
          fullScanReportCreatedAt: chatFile.fullScanReportCreatedAt,
          fullScanReportUpdatedAt: chatFile.fullScanReportUpdatedAt,
          errCode: chatFile.errCode,
          hasPii: chatFile.hasPii,
          hasPromptInjection: chatFile.hasPromptInjection,
          detectedPii: chatFile.detectedPii,
          piiFileStatus: chatFile.piiFileStatus,
          scanMalwareStatus: chatFile.scanMalwareStatus,
          malwareRating: chatFile.malwareRating,
          isApproved: true,
          status: 'APPROVED',
          errorMsg: chatFile.errorMsg,
          filetype: contentType,
          fullScanReportVersion: chatFile.fullScanReportVersion,
          scanMalwareVersion: chatFile.scanMalwareVersion,
          autoIndex: true,
          verifyStatus: chatFile.verifyStatus,
          verifyErrCode: chatFile.verifyErrCode,
          verifyErrorMsg: chatFile.verifyErrorMsg,
          model: {
            connect: {
              id: model.id,
            },
          },
          group: {
            connect: {
              id: model.groupId,
            },
          },
          uploader: {
            connect: {
              id: userId,
            },
          },
        },
      });
      //delete chat file
      if (updatedDoc) {
        this.logger.log(`delete chat file [${chatFile.id}]`);
        await this.deleteChatFile(tx, chatFile.groupId, chatFile.userId, chatFile.id);
      }
      //store moved file mapping
      const range = files.find((file) => file.file_id === chatFile.s3Basename).range;
      modelFileDocIds.push({
        file_id: chatFile.s3Basename,
        range: range,
        new_file_id: updatedDoc.docId,
      });
    }
    return modelFileDocIds;
  }

  getSupportedFileExtensions(chatApproach: ChatFilesApproach) {
    return this.validFileExtensions[chatApproach];
  }

  async getValidFiles(s3basenames: string[]) {
    const chatFiles = await this.prisma.chatFile.findMany({
      where: {
        s3Basename: {
          in: s3basenames,
        },
        expiresAt: { gte: new Date() },
      },
      select: {
        s3Basename: true,
      },
    });
    return chatFiles.map((chatFile) => chatFile.s3Basename);
  }

  async scanMalwareOrPii(
    chatFile: ChatFile,
    s3Key: string,
    env: string,
    file?: Express.Multer.File | Readable,
    isFromUpload?: boolean,
  ) {
    const byPassMalwareScan = await this.featureFlagService.getDefaultFeatureFlagFromDb(
      FeatureFlagKey.BYPASS_SCAN_MALWARE,
    );
    //if bypass malware scan is disabled, scan malware and will scan pii after malware scan completed
    if (byPassMalwareScan?.isEnabled) {
      //pii scan
      await this.llmModelsService.chatFilePiiScan(chatFile, s3Key);
      if (isFromUpload) {
        const callBackUrl = `/v1/embeddings/verify/chatFile/${chatFile.id}`;
        await this.llmModelsService.verifyFile(
          'chatFile',
          s3Key,
          chatFile,
          callBackUrl,
          chatFile.userId,
        );
      }
    } else {
      //malware scan
      if (chatFile.scanMalwareStatus === null) {
        if (!file) {
        const s3Response = await this.s3Service.get(
            this.configService.get<string>(`s3.staticFilesBuckets.${env}`),
            s3Key,
          );
          file = s3Response.Body as Readable;
        }
        await this.fortiSanboxService.scanChatFileMalware(chatFile, file);
      } else if (
        //if aready scanned and clean, then scan pii
        chatFile.scanMalwareStatus === ScanMalwareStatus.COMPLETED &&
        chatFile.malwareRating === 'Clean'
      ) {
        await this.llmModelsService.chatFilePiiScan(chatFile, s3Key);
      }
    }
  }

  async reScanFile(groupId: number, userId: number, chatFileId: number) {
    const chatFile = await this.prisma.chatFile.findFirst({
      where: {
        id: chatFileId,
        groupId,
        userId,
        expiresAt: { gte: new Date() },
      },
      include: {
        group: true,
      },
    });
    if (!chatFile) {
      throw new ApiException(ErrorCode.CHAT_FILE_NOT_FOUND);
    }
    const s3Key = this.getS3FileKey(groupId, userId, chatFile.s3Basename, ChatFileType.QUESTION);
    this.scanMalwareOrPii(chatFile, s3Key, chatFile.group.env).catch((error: Error) => {
      this.logger.error(`scanMalwareOrPii : ${error.message}`, error.stack);
    });
    return chatFile;
  }

  async updateChatFileSkipPiiScan(id: number, skipPiiScan: boolean) {
    return await this.prisma.chatFile.update({
      where: { id },
      data: {
        hasPiiScanSkip: skipPiiScan,
      },
    });
  }

  async monitorChatFileSizeLog(
    entryType: string,
    chatFileType: ChatFileType,
    file: Express.Multer.File,
    groupId: number,
    userId: number,
  ) {
    if (chatFileType === ChatFileType.QUESTION) {
      const chatWithFileConfig = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
        null,
        FeatureFlagKey.BOT_CHAT_WITH_FILE_CONFIG,
      );
      const maxFileSize = (chatWithFileConfig?.metaData?.['maxFileSize'] as number) ?? 7340032;
      if (file?.size >= maxFileSize) {
        this.logger.log(
          `monitor entry [${entryType}] file size [${file.size}] of file [${file.originalname}] exceeded ${maxFileSize} - userId: ${userId}, groupId: ${groupId}`,
        );
      }
    }
  }
}
